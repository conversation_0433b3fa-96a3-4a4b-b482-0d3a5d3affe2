{"name": "ddebs-train-mobile-cli", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "test-build": "vue-cli-service build --mode test_build", "dev-build": "vue-cli-service build --mode dev_build", "lint": "vue-cli-service lint", "format": "prettier --write ."}, "dependencies": {"@vueuse/core": "^10.9.0", "axios": "^0.27.2", "core-js": "^3.8.3", "dayjs": "^1.11.13", "echarts": "^5.5.0", "lodash": "^4.17.21", "mitt": "^3.0.0", "moment": "^2.29.4", "normalize.css": "^8.0.1", "pinia": "^2.0.18", "vant": "^4.9.0", "vconsole": "^3.14.7", "vue": "^3.2.13", "vue-router": "^4.0.13", "xgplayer": "2.31.8"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "autoprefixer": "^10.4.14", "eslint": "^7.32.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-vue": "^8.0.3", "node-sass": "^7.0.3", "postcss": "^8.4.24", "postcss-px-to-viewport": "^1.1.1", "prettier": "^3.2.5", "sass": "^1.54.4", "sass-loader": "^13.1.0", "tailwindcss": "^3.3.2", "unplugin-vue-components": "^0.22.4"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}