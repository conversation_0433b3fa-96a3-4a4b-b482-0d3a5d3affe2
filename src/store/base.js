import { defineStore } from 'pinia';

export const useBaseStore = defineStore('base', {
  state: () => {
    return {
      currentCompany: {},
      currentUser: {},
      platform: '', // 1安卓 2iOS
      navBarHeight: 0,
      isAuthority: false, // 是否有查看统计权限
      uploadConfig: '',
      uploadBucket: '',
      statistcTime: '',
      statistcUserInfo: {},
      userUploadConfig: {},
      version: '',
      // 自己的
      currentTabBarIndex: 0,
      videoTimeInfo: {},
    };
  },
  actions: {
    upVideoRecord({ fieId, second }) {
      console.log('来到了这里 啊啊啊啊');
    },
    changeVal(value) {
      this.currentTabBarIndex = value;
    },
  },
});
