import { createApp } from 'vue';
import App from './App.vue';
import router from './router';
import { createPinia } from 'pinia';
import { globalRegister } from '@/global/index.js';
import 'normalize.css';
import './main.css';
import 'vant/lib/index.css';
import './appCallBack';
import VConsole from 'vconsole';

const VUE_APP_NODE = process.env.VUE_APP_NODE;

console.log('ENV', VUE_APP_NODE);

if (VUE_APP_NODE !== 'production') {
  new VConsole();
}

const app = createApp(App);

// app.use(globalRegister) // 两种写法都可已
globalRegister(app);
app.use(router).use(createPinia()).mount('#app');
