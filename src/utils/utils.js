// 截取指定长度的字符串
export function getStrLength(str, maxLength, type) {
  if (str.length > maxLength) {
    return str.substring(0, maxLength) + '...';
  } else {
    return str;
  }
}

// 将毫秒转为分钟
export function formatTimeToMinutes(time) {
  let min = Math.floor(time / 1000 / 60);
  let sec = Math.floor((time / 1000) % 60);
  min = min < 10 ? '0' + min : min;
  sec = sec < 10 ? '0' + sec : sec;
  return min + ':' + sec;
}

// 毫秒转小时:分钟
export function formatTimeToHour(time, isPadding = true) {
  if (!time) return { hour: '0', min: '0' };
  let hour = Math.floor(time / 1000 / 60 / 60);
  let min = Math.floor((time / 1000 / 60) % 60);
  if (!isPadding) return { hour, min };
  hour = hour < 10 ? '0' + hour : hour;
  min = min < 10 ? '0' + min : min;
  return { hour, min };
}

// 毫秒转为小时，保留1位小数
export function formatTimeToHourFloat(time) {
  let hour = time / 1000 / 60 / 60;
  return hour.toFixed(1);
}

// 将数字不足两位前面补0
export function formatNumberToXX(num) {
  return num < 10 ? '0' + num : num;
}

// KB => MB
export function formatKBToMB(size) {
  const num = (size / 1024).toFixed(2);
  return num > 0 ? num : 0.01;
}

// 获取一年的周数量
export function getWeeksOfYear(year) {
  const firstDate = new Date(`${year}-01-01`);
  const lastDate = new Date(`${year + 1}-01-01`);

  let date = firstDate;
  let month = firstDate.getMonth();
  let weekNumber = 1;
  const weeks = [];

  while (date <= lastDate) {
    weeks[month] = weeks[month] || [];
    ((weekNumber) => {
      weeks[month].push(weekNumber);
    })(weekNumber);
    date.setDate(date.getDate() + 7);
    if (date.getFullYear() === year + 1) break;
    weekNumber++;
    if (date.getMonth() !== month) {
      month = date.getMonth();
    }
  }

  return { weeks, weekNumber };
}

// 获取当前周是一年的第几周
export function getWeekOfYear(date) {
  const beginDate = new Date(date.getFullYear(), 0, 1);
  let endWeek = date.getDay();
  if (endWeek === 0) endWeek = 7;
  let beginWeek = beginDate.getDay();
  if (beginWeek === 0) beginWeek = 7;
  const millisDiff = date.getTime() - beginDate.getTime();
  const dayDiff = Math.floor((millisDiff + (beginWeek - endWeek) * (24 * 60 * 60 * 1000)) / 86400000);
  return Math.ceil(dayDiff / 7) + 1;
}
