// import bus from '@/prototype/bus'
import axios from 'axios';

const api_url = process.env.VUE_APP_API;
const fetch = axios.create({
  timeout: 10000,
});

fetch.interceptors.request.use(function (config) {
  let token = window.sessionStorage.getItem('__token__');
  // let token = 'Bearer 82230356-876c-4d80-8229-b7170abab889'
  token = token === 'undefined' ? '' : token;
  config.headers.common['Authorization'] = token;
  return config;
});

fetch.interceptors.response.use(
  function (response) {
    const { status, config } = response;
    let msg = response.data.msg || '未知错误';
    if (status === 200) {
      return response.data;
    }
    if (status === 500) {
      msg = '服务器繁忙，稍后重试';
      // bus.$emit('loading', !1)
    }
    // bus.$emit('__error__', msg)
    // return Promise.reject(new Error('服务器繁忙，稍后重试'))

    return Promise.reject(new Error(msg));
  },
  function (error) {
    // bus.$emit('loading', !1)
    return Promise.reject(error);
  },
);

export default fetch;
