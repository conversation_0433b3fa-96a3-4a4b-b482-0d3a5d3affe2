import { platform } from '@/config';
import { useBaseStore } from '@/store/base';
// const baseStore = useBaseStore()

// const currentTabBarIndex = baseStore.currentTabBarIndex

let baseStore;
setTimeout(() => {
  baseStore = useBaseStore(); // 放到宏任务队列才能正常执行
});

export const appBack = (router, route) => {
  console.log('route.fullPath:--', route.fullPath);
  console.log('执行appBack-----------');
  console.log('route.query', route.query);
  if (
    route.fullPath === '/' ||
    route.fullPath === '/course-table' ||
    route.fullPath === '/my-bank' ||
    route.fullPath === '/my-exam' ||
    route.fullPath === '/person-page' ||
    (route.query && Object.prototype.hasOwnProperty.call(route.query, 'platform'))
  ) {
    console.log('返回app');
    backClient();
  } else {
    console.log('执行router.back()------------');
    if (route.fullPath === '/course') {
      baseStore.currentTabBarIndex = 0;
    }
    if (route.fullPath === '/') {
      baseStore.currentTabBarIndex = 1;
    }
    router.back();
  }
};

export const backClient = () => {
  console.log('app直接回退');
  const plat = window.sessionStorage.getItem('__platform__');
  if (plat + '' === platform.ios) {
    window.webkit.messageHandlers.client_goBack.postMessage('');
  } else if (plat + '' === platform.android) {
    window.DDBESOFFICE.client_goBack();
  }
};

// 时间戳转化为时间
export function parseTime(time, Symbol = '/') {
  if (time === '') return '';
  var date = new Date(time); // 获取一个时间对象
  date.getFullYear(); // 获取完整的年份(4位,1970)
  date.getMonth(); // 获取月份(0-11,0代表1月,用的时候记得加上1)
  date.getDate(); // 获取日(1-31)
  date.getTime(); // 获取时间(从1970.1.1开始的毫秒数)
  date.getHours(); // 获取小时数(0-23)
  date.getMinutes(); // 获取分钟数(0-59)
  date.getSeconds(); // 获取秒数(0-59)
  let minutes = date.getMinutes() >= 10 ? date.getMinutes() : `0${date.getMinutes()}`;
  return `${date.getMonth() + 1}${Symbol}${date.getDate()}`;
}

export function parseTimeToDate(time, Symbol = '/') {
  if (time === '') return '';
  var date = new Date(time); // 获取一个时间对象
  date.getFullYear(); // 获取完整的年份(4位,1970)
  date.getMonth(); // 获取月份(0-11,0代表1月,用的时候记得加上1)
  date.getDate(); // 获取日(1-31)
  date.getTime(); // 获取时间(从1970.1.1开始的毫秒数)
  date.getHours(); // 获取小时数(0-23)
  date.getMinutes(); // 获取分钟数(0-59)
  date.getSeconds(); // 获取秒数(0-59)
  let minutes = date.getMinutes() >= 10 ? date.getMinutes() : `0${date.getMinutes()}`;
  return `${date.getMonth() + 1}月${date.getDate()}日`;
}

export function parseTimeTomin(time, Symbol = '/') {
  if (time === '') return '';
  var date = new Date(time); // 获取一个时间对象
  date.getFullYear(); // 获取完整的年份(4位,1970)
  date.getMonth(); // 获取月份(0-11,0代表1月,用的时候记得加上1)
  date.getDate(); // 获取日(1-31)
  date.getTime(); // 获取时间(从1970.1.1开始的毫秒数)
  date.getHours(); // 获取小时数(0-23)
  date.getMinutes(); // 获取分钟数(0-59)
  date.getSeconds(); // 获取秒数(0-59)
  let minutes = date.getMinutes() >= 10 ? date.getMinutes() : `0${date.getMinutes()}`;
  return `${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours()}: ${minutes}`;
}

export function parseTimeToFullDate(time, Symbol = '/') {
  if (time === '') return '';
  const date = new Date(time); // 获取一个时间对象
  let minutes = date.getMinutes() >= 10 ? date.getMinutes() : `0${date.getMinutes()}`;
  return date.getFullYear() + Symbol + (date.getMonth() + 1) + Symbol + date.getDate() + ' ' + date.getHours() + ':' + minutes;
}

// 判断课程状态
export function getCourseState(startTime, endTime, type = 1, status = 0) {
  // console.log('看看状态参数', startTime, endTime, type, status);
  if (type === 0) return '线下';
  if (status === 1) return '已完成';
  const currentTime = new Date().getTime();
  if (currentTime < startTime) {
    return '未开课';
  } else if (currentTime < endTime) {
    return '已开课';
  } else {
    return '已结束';
  }
}
