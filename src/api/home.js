import fetch from '../utils/fetch';
import moment from 'moment';

const baseUrl = process.env.VUE_APP_API;
const url = `${baseUrl}/training`;

// 获取全部课程
export const getAllCourseList = (page, size, isMyCourse, keyword, categoryId, premiumType = 0) =>
  fetch({
    url: `${url}/api/v1/course/app/index`,
    params: {
      page,
      size,
      isMyCourse,
      keyword,
      categoryId,
      premiumType
    },
  });

// 获取计划
export const getPlanList = (page, size) =>
  fetch({
    url: `${url}/api/v1/plan/index?page=${page}&size=${size}`,
  });

// 获取课程通过计划
export const getCourseListByPlanId = (planId) =>
  fetch({
    url: `${url}/api/v1/plan/${planId}/course?isStatisticsDuration=false`,
  });

// 获取课程通过定级培训
export const getCourseListByRatingId = (ratingId, keyword) =>
  fetch({
    url: `${url}/api/v1/rating/${ratingId}/course?isStatisticsDuration=false&key_word=${keyword}`,
  });

// 获取已结束计划
export const getEndedPlanList = (page, size, year, month) => {
  const startTime = `${year}-${month}-01 00:00:00`;
  const endTime = `${year}-${month}-31 23:59:59`;

  return fetch({
    url: `${url}/api/v1/plan/ended?page=${page}&size=${size}&startTime=${startTime}&endTime=${endTime}`,
  });
};

// 获取已完成的计划
export const getFinishedPlanList = (page, size) =>
  fetch({
    url: `${url}/api/v1/plan/finished?page=${page}&size=${size}`,
  });

// 获取分类
export const getCategoryList = () => {
  return fetch({
    url: `${url}/api/v1/category/place`,
  });
};

export const getAllCategoryList = () => {
  return fetch({
    url: `${url}/api/v1/category/all`,
  });
};

// 获取部门下的定级培训
export const getRatingTrain = (categoryId) => {
  return fetch({
    url: `${url}/api/v1/rating/index?categoryId=${categoryId}&isStatisticsDuration=false`,
  });
};

// 获取活动及考试红点
export const getRedDot = () => fetch({
  url: `${url}/api/v1/user/red-point`,
})
