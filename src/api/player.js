import fetch from '../utils/fetch';

const baseUrl = process.env.VUE_APP_API;
const url = `${baseUrl}/training`;
const ce_url = 'https://api.ddbes.com/file';

// 上报视频进度
export const reportVideoProgress = (courseId, hourId, isPlanHour, data) =>
  fetch({
    url: `${url}/api/v1/course/${courseId}/hour/${hourId}/record?isPlanHour=${isPlanHour}`,
    method: 'post',
    data,
  });

// 上报观看状态
export const reportVideoStatus = (courseId, hourId, isPlanHour, data) =>
  fetch({
    url: `${url}/api/v1/course/${courseId}/hour/${hourId}/ping?isPlanHour=${isPlanHour}`,
    method: 'post',
    data,
  });

// 获取视频播放地址及token
export const getFileToken = (params) =>
  fetch({
    url: `${ce_url}/api/v1/preview/getViewUrlWebPath`,
    params: params,
  });

// 获取课时详情
export const getCourseHourDetail = (courseId, hourId, planId) =>
  fetch({
    url: `${url}/api/v1/course/${courseId}/hour/${hourId}?planId=${planId}`,
  });

// 上报视频完成
export const reportVideoFinish = (courseId, hourId, isPlanHour) =>
  fetch({
    url: `${url}/api/v1/course/${courseId}/hour/${hourId}/finish?isPlanHour=${isPlanHour}`,
    method: 'post',
  });
