import fetch from '../utils/fetch';

const baseUrl = process.env.VUE_APP_API;
const url = `${baseUrl}/training`;

// 获取学习信息
export const getBaseInfo = (startTime, endTime) =>
  fetch({
    url: `${url}/api/v1/user-page/base-info?startTime=${startTime}&endTime=${endTime}`,
  });

// 获取最近学习课程
export const getRecentCourses = (startTime, endTime, limit) =>
  fetch({
    url: `${url}/api/v1/user-page/course/recent`,
    params: { startTime, endTime, limit },
  });

// 获取个人统计数据
export const getPersonStatistics = (timeType, timeValue) =>
  fetch({
    url: `${url}/api/v2/user/info/detail?timeType=${timeType}&timeValue=${timeValue}`,
  });

// ------------------ 排行榜 ------------------

// 学员时长排行
export const getDurationRank = ({ startTime, endTime, limit, areaType }) =>
  fetch({
    url: `${url}/api/v1/user-page/study-time/ranking?startTime=${startTime}&endTime=${endTime}&limit=${limit}&areaType=${areaType}`,
  });

// 学员通过率排行
export const getCompletionRateRank = ({ startTime, endTime, limit, areaType }) =>
  fetch({
    url: `${url}/api/v1/user-page/pass-rate/ranking?startTime=${startTime}&endTime=${endTime}&limit=${limit}&areaType=${areaType}`,
  });

// 课程观看次数排行
export const getCourseRank = ({ startTime, endTime, limit }) =>
  fetch({
    url: `${url}/api/v1/user-page/course-study-num/ranking?startTime=${startTime}&endTime=${endTime}&limit=${limit}`,
  });

// 课程收藏数量排行
export const getCourseFavoriteRank = ({ startTime, endTime, limit }) =>
  fetch({
    url: `${url}/api/v1/user-page/course-favorite-num/ranking?startTime=${startTime}&endTime=${endTime}&limit=${limit}`,
  });

// ------------------ 课程收藏 ------------------

// 获取收藏课程
export const getFavorites = () =>
  fetch({
    url: `${url}/api/v1/user-page/course/favorites`,
  });

// 添加收藏课程
export const addFavorite = (courseIds) =>
  fetch({
    url: `${url}/api/v1/user-page/course/favorites`,
    method: 'POST',
    data: courseIds,
  });

// 取消收藏
export const cancelFavorite = (ids) =>
  fetch({
    url: `${url}/api/v1/user-page/course/favorites/delete-multi`,
    method: 'POST',
    data: ids,
  });

// ------------------ 培训统计 ------------------

// 获取培训统计数据
export const getTrainingStatistics = (startTime, endTime, placeId) =>
  fetch({
    url: `${url}/api/v1/user-page/statistic/overview?startTime=${startTime}&endTime=${endTime}&placeId=${placeId}`,
  });

// 获取培训计划进展
export const getTrainingPlanProgress = (startTime, endTime, placeId, type, page, size) =>
  fetch({
    url: `${url}/api/v1/user-page/statistic/plan-list?startTime=${startTime}&endTime=${endTime}&placeId=${placeId}&type=${type}&page=${page}&size=${size}`,
  });

// 获取计划详情
export const getTrainingPlanDetail = (planId, type) =>
  fetch({
    url: `${url}/api/v1/user-page/statistic/plan-detail?planId=${planId}&type=${type}`,
  });

// 通知
export const sendNotification = (planId, notification, type) => fetch({
  url: `${url}/api/v1/user-page/send-notification`,
  method: 'POST',
  data: { planId, notification, type },
});


