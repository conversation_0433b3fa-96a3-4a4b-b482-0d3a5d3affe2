import fetch from '../utils/fetch';

const baseUrl = process.env.VUE_APP_API;
const url = `${baseUrl}/training`;

// 获取个人数据
export const getPersonData = (timeType, timeValue) =>
  fetch({
    url: `${url}/api/v1/user/info/detail?timeType=${timeType}&timeValue=${timeValue}`,
  });

// 获取我的附件
export const getMyAttachment = (keyword, year) =>
  fetch({
    url: `${url}/api/v1/user/info/annex?name_keyword=${keyword}&year=${year}`,
  });

// 获取看过的课程
export const getCompletedCourse = (keyword, inPlan) =>
  fetch({
    url: `${url}/api/v1/user/info/course?name_keyword=${keyword}&inPlan=${inPlan}`,
  });

// 获取我的成绩
export const getCourseScore = (year, isPass, keyword) =>
  fetch({
    url: `${url}/api/v1/user/info/course/score?year=${year}&isPass=${isPass}&name_keyword=${keyword}`,
  });
