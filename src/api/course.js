import fetch from '../utils/fetch';

// const baseUrl = import.meta.env.VITE_APP_API
const baseUrl = process.env.VUE_APP_API;
const url = `${baseUrl}/training/course/app/v1`;

// 获取课程详情
export const getCourseDetail = (id, type, timeId) =>
  fetch({
    url: `${url}/course/${id}`,
    params: {
      type,
      timeId,
    },
  });

// 获取我的课程
export const getMyCourse = () =>
  fetch({
    url: `${url}/myCourse`,
  });

// 搜索课程
export const searchCourse = (key) =>
  fetch({
    url: `${url}/myCourse/content/${key}`,
  });

// 获取url
export const getUrl = (id) =>
  fetch({
    url: `${url}/file/url/${id}`,
  });

// 获取观看时长
export const getStudyTime = (data) =>
  fetch({
    url: `${url}/statistic/study_time`,
    params: data,
  });
// 获取观看记录
export const getRecord = (data) =>
  fetch({
    url: `${url}/watch_record`,
    params: data,
  });

// 上传观看记录
export const upRecord = (data) =>
  fetch({
    url: `${url}/watch_record`,
    method: 'post',
    data,
  });
