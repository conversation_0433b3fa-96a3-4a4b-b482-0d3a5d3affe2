import fetch from '../utils/fetch';

const baseUrl = process.env.VUE_APP_API;
const url = `${baseUrl}/training`;

// 获取课时试题
export const getCourseHourQuestion = (courseId, hourId, planId = '') =>
  fetch({
    url: `${url}/api/v1/course-hour-test/course/${courseId}/hour/${hourId}/paper?planId=${planId}`,
  });

// 提交课时试题
export const submitCourseHourQuestion = (data) =>
  fetch({
    method: 'put',
    url: `${url}/api/v1/course-hour-test/paper`,
    data,
  });

// 获取考试结果
export const getTestResult = (courseId, hourId, planId = '') =>
  fetch({
    url: `${url}/api/v1/course-hour-test/course/${courseId}/hour/${hourId}/paper/record?planId=${planId}`,
  });

// 获取随堂考试错题本
export const getWrongQuestionBank = (page, size) =>
  fetch({
    url: `${url}/api/v1/course-hour-test/wrong-question/card?page=${page}&size=${size}`,
  });

// 提交错题
export const submitWrongQuestion = (id, data) =>
  fetch({
    method: 'put',
    url: `${url}/api/v1/course-hour-test/wrong-question/${id}`,
    data,
  });
