import fetch from '../utils/fetch';

const baseUrl = process.env.VUE_APP_API;
const url = `${baseUrl}/training`;

// 获取考试
export const getExamList = (status, year, month) =>
  fetch({
    url: `${url}/api/v1/plan-test/index`,
    params: {
      status,
      startTime: `${year}-${month}-01 00:00:00`,
      endTime: `${year}-${month}-31 23:59:59`,
    },
  });

// 获取试卷
export const getExamPaper = (id, planTestId, release) => {
  return fetch({
    url: `${url}/api/v1/plan-test/${id}/paper?planTestId=${planTestId}&isRelease=${release}`,
  });
};

// 提交试卷
export const submitExamPaper = (data) => {
  return fetch({
    url: `${url}/api/v1/plan-test/submit`,
    method: 'post',
    data,
  });
};

// 获取考试结果
export const getExamResult = (planId, planTestId, planTestReleaseId) => {
  return fetch({
    url: `${url}/api/v1/plan-test/submit/record?planId=${planId}&planTestId=${planTestId}&planTestReleaseId=${planTestReleaseId}`,
    method: 'post',
  });
};
