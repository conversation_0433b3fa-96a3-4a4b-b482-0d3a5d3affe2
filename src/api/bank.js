import fetch from '../utils/fetch';

const baseUrl = process.env.VUE_APP_API;
const url = `${baseUrl}/training`;

// 获取全部题库
export const getAllQuestionBank = (page, size, keyword, categoryId) =>
  fetch({
    url: `${url}/api/v1/question-bank?page=${page}&size=${size}&bankName_keyword=${keyword}&categoryId=${categoryId}`,
  });

// 获取题库答题卡
export function getQuestionBankAnswerCard(bankId) {
  return fetch({
    url: `${url}/api/v1/question-bank/${bankId}/card`,
  });
}

// 通过题库id获取题库问题列表
export function getQuestionListByIds(ids) {
  return fetch({
    method: 'POST',
    url: `${url}/api/v1/question-bank/question`,
    data: { questionIds: ids },
  });
}

// 提交题目
export function submitQuestion(id, data) {
  return fetch({
    method: 'POST',
    url: `${url}/api/v1/question-bank/${id}/`,
    data,
  });
}

// 收藏题目
export function markQuestion(id, questionId) {
  return fetch({
    method: 'POST',
    url: `${url}/api/v1/question-bank/${id}/question-mark?questionId=${questionId}`,
  });
}

// 取消收藏
export function unmarkQuestion(id, questionId) {
  return fetch({
    method: 'DELETE',
    url: `${url}/api/v1/question-bank/${id}/question-mark?questionId=${questionId}`,
  });
}

// 重置答题卡记录
export function resetQuestionRecord(id) {
  return fetch({
    method: 'PUT',
    url: `${url}/api/v1/question-bank/${id}/card/reset`,
  });
}

// 获取收藏夹题库
export function getCollectBank(page, size, keyword) {
  return fetch({
    url: `${url}/api/v1/question-bank/question-mark/question-bank?page=${page}&size=${size}&questionBankName_keyword=${keyword}`,
  });
}

// 获取收藏夹题库答题卡
export function getMarkBankCard(id) {
  return fetch({
    url: `${url}/api/v1/question-bank/${id}/question-mark/card`,
  });
}

// 重置收藏答题卡记录
export function resetMarkBankRecord(id) {
  return fetch({
    method: 'PUT',
    url: `${url}/api/v1/question-bank/${id}/question-mark/card/reset`,
  });
}

// 提交收藏夹题库题目
export function submitMarkQuestion(id, data) {
  return fetch({
    method: 'POST',
    url: `${url}/api/v1/question-bank/${id}/question-mark/submit`,
    data,
  });
}

// 获取错题本题库
export function getWrongBank(page, size, keyword, categoryId) {
  return fetch({
    url: `${url}/api/v1/question-bank/question-wrong/question-bank?page=${page}&size=${size}&bankName_keyword=${keyword}&category=${categoryId}`,
  });
}

// 获取错题本题库答题卡
export function getWrongBankCard(id) {
  return fetch({
    url: `${url}/api/v1/question-bank/${id}/question-wrong/card`,
  });
}

// 提交错题本题库题目
export function submitWrongQuestion(id, data) {
  return fetch({
    method: 'POST',
    url: `${url}/api/v1/question-bank/${id}/question-wrong/submit`,
    data,
  });
}
