<template>
  <div id="app" class="flex flex-col h-screen overflow-hidden text-base font-base">
    <div ref="navHeightBar" :style="{ height: navHeight + 'px' }"></div>
    <router-view class="flex-1" v-slot="{ Component }">
      <transition name="fade" mode="out-in">
        <component :is="Component" />
      </transition>
    </router-view>
  </div>
</template>

<script setup>
import { onMounted, ref } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { nameSpace } from '@/config';
import { appBack } from '@/utils';

const router = useRouter();
const route = useRoute();

let url = window.location.href;
let arrUrl = url.split('platform=');
let params = arrUrl[1];
if (params) {
  console.log('query.platformaa', params[0]);
  sessionStorage.setItem('__platform__', params[0]);
}

const navHeightBar = ref(null);
const navHeight = ref(40);

onMounted(() => {
  setTimeout(() => {
    navHeightBar.value.style.height = sessionStorage.getItem('__barHeight__') + 'px';
  }, 500);
});

// 安卓点击返回按钮
window[nameSpace].onBackPress = function () {
  appBack(router, route);
};
</script>

<style>
#app {
  width: 750px;
  overflow: hidden;
  background: #f5f6f7;
  height: 100vh;
  display: flex;
  flex-direction: column;
  font-size: 28px;
  line-height: normal;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
