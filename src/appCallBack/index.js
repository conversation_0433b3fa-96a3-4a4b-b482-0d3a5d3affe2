// 跟app交互的方法

import { useBaseStore } from '@/store/base';
import { nameSpace, platform } from '@/config';
import Bus from '@/utils/bus.js';

let baseStore;
setTimeout(() => {
  baseStore = useBaseStore(); // 放到宏任务队列才能正常执行
}, 500);

window[nameSpace] = {};

const saveClientInfo = (data) => {
  window.sessionStorage.setItem('__token__', data.token);
  console.log('__token__', data.token);
  // baseStore.currentCompany = data.company;
  window.sessionStorage.setItem('__company__', JSON.stringify(data.company));
  // baseStore.currentUser = data.user;
  window.sessionStorage.setItem('__user__', JSON.stringify(data.user));
  Bus.emit('token_save');
};

window[nameSpace].updateClientInfo = function (data) {
  if (data) {
    if (typeof data === 'object') {
      saveClientInfo(data);
    } else {
      const parseData = JSON.parse(data);
      saveClientInfo(parseData);
    }
    const _platform = window.sessionStorage.getItem('__platform__');
    if (_platform + '' === platform.ios) {
      window.webkit.messageHandlers.client_getStatusBarHeight.postMessage('');
    } else if (_platform + '' === platform.android) {
      window.DDBESOFFICE.client_getStatusBarHeight();
    }
  }
};

// 保存token到本地
window[nameSpace].updateToken = function (data) {
  if (!data) {
    console.log('未获取到token');
  } else {
    if (typeof data === 'object') {
      window.sessionStorage.setItem('__token__', data.token);
    } else {
      window.sessionStorage.setItem('__token__', data);
    }
  }
};

// 保存当前公司信息到本地
window[nameSpace].updateCompanyInfo = function (data) {
  if (typeof data !== 'object') {
    data = JSON.parse(data);
  }
  // baseStore.currentCompany = data;
  window.sessionStorage.setItem('__company__', JSON.stringify(data));
};

// 保存登录用户信息到本地
window[nameSpace].updateUserInfo = function (data) {
  if (typeof data !== 'object') {
    data = JSON.parse(data);
  }
  // baseStore.currentUser = data;
  window.sessionStorage.setItem('__user__', JSON.stringify(data));
};

window[nameSpace].updateStatusBarHeight = function (num) {
  console.log('获取的导航栏高度');
  window.sessionStorage.setItem('__barHeight__', num);
  // baseStore.navBarHeight = num || 0;
  window.sessionStorage.setItem('__navBarHeight__', num || 0);
};

window[nameSpace].updateCosInfo = function (data) {
  if (typeof data !== 'object') {
    data = JSON.parse(data);
  }
  // baseStore.uploadConfig = data;
  window.sessionStorage.setItem('__uploadConfig__', JSON.stringify(data));
};

window[nameSpace].getStorage = function (value) {
  const info = JSON.parse(value).JS_storage.select_category;
  sessionStorage.setItem('select_category', JSON.stringify(info));
};

window[nameSpace].applicationWillResignActive = function () {
  Bus.emit('app_back');
};
