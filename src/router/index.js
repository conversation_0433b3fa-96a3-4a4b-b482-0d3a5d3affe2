import { createRouter, createWebHashHistory } from 'vue-router';
import Index from '@/views/Index/index.vue';
import Home from '@/views/Home/index.vue';
import { nameSpace, platform } from '@/config';
import Bus from '@/utils/bus';

const routes = [
  {
    path: '/',
    name: 'Index',
    component: Index,
    redirect: '/home',
    children: [
      {
        path: '/home',
        name: 'Home',
        component: Home,
      },
      {
        path: '/course-table',
        name: 'CourseTable',
        component: () => import('@/views/CourseTable/index.vue'),
      },
      {
        path: '/my-bank',
        name: 'MyBank',
        component: () => import('@/views/MyBank/index.vue'),
      },
      {
        path: '/my-exam',
        name: 'MyExam',
        component: () => import('@/views/MyExam/index.vue'),
      },
      {
        path: '/person-page',
        name: 'PersonPage',
        component: () => import('@/views/Profile/Profile.vue'),
      },
    ],
  },
  {
    path: '/star-course',
    name: 'StarCourse',
    component: () => import('@/views/Home/StarCourse.vue'),
  },
  {
    path: '/profile/recent-viewed',
    name: 'RecentViewed',
    component: () => import('@/views/Profile/RecentViewed.vue'),
  },
  {
    path: '/profile/collection',
    name: 'Collection',
    component: () => import('@/views/Profile/Collection.vue'),
  },
  {
    path: '/profile/statistics',
    name: 'Statistics',
    component: () => import('@/views/Profile/statistics/Statistics.vue'),
  },
  {
    path: '/profile/statistic-rank',
    name: 'StatisticRank',
    component: () => import('@/views/Profile/statistics/StatisticRank.vue'),
  },
  {
    path: '/profile/learning-statistics',
    name: 'LearningStatistics',
    component: () => import('@/views/Profile/LearningStatistics.vue'),
  },
  {
    path: '/profile/user-rank',
    name: 'UserRank',
    component: () => import('@/views/Profile/UserRank.vue'),
  },
  {
    path: '/profile/course-rank',
    name: 'CourseRank',
    component: () => import('@/views/Profile/CourseRank.vue'),
  },
  {
    path: '/profile/statistic-course',
    name: 'StatisticCourse',
    component: () => import('@/views/Profile/statistics/StatisticCourse.vue'),
  },
  {
    path: '/profile/statistic-notice',
    name: 'StatisticNotice',
    component: () => import('@/views/Profile/statistics/StatisticNotice.vue'),
  },
  {
    path: '/course-wrong-table',
    name: 'CourseWrongTable',
    component: () => import('@/views/CourseTable/CourseWrongTable.vue'),
  },
  {
    path: '/category',
    name: 'Category',
    component: () => import('@/views/Category/index.vue'),
  },
  {
    path: '/details/:courseId/:planId?',
    name: 'Details',
    component: () => import('@/views/Details/index.vue'),
  },
  {
    path: '/file-preview/:name/:courseId?/:hourId?/:planId?',
    name: 'FilePreview',
    component: () => import('@/views/Preview/PreviewFile.vue'),
  },
  {
    path: '/image-preview/:name/:courseId?/:hourId?/:planId?',
    name: 'ImagePreview',
    component: () => import('@/views/Preview/PreviewImage.vue'),
  },
  {
    path: '/player/:courseId/:hourId/:planId?',
    name: 'Player',
    component: () => import('@/views/Player/index.vue'),
  },
  {
    path: '/simple-player',
    name: 'SimplePlayer',
    component: () => import('@/views/Preview/PreviewVideo.vue'),
  },
  {
    path: '/remind',
    name: 'Remind',
    component: () => import(/* webpackChunkName: "remind" */ '@/views/remind/Remind.vue'),
  },
  {
    path: '/detail',
    name: 'Detail',
    component: () => import(/* webpackChunkName: "detail" */ '@/views/detail/Detail.vue'),
  },
  {
    path: '/course',
    name: 'Course',
    component: () => import(/* webpackChunkName: "course" */ '@/views/course/Course.vue'),
    meta: {
      isKeepAlive: true,
      isMain: false,
    },
    beforeEnter: (to, from) => {
      if (from.fullPath === '/') {
        to.meta.isMain = true;
      } else {
        to.meta.isMain = false;
      }
      return true;
    },
  },
  {
    path: '/video',
    name: 'Video',
    component: () => import(/* webpackChunkName: "video" */ '@/views/video/Video.vue'),
  },
  {
    path: '/test/:courseId/:hourId/:planId?',
    name: 'Test',
    component: () => import('@/views/Test/index.vue'),
  },
  {
    path: '/test-index/:mode',
    name: 'TestIndex',
    component: () => import('@/views/Test/test.vue'),
  },
  {
    path: '/test-result',
    name: 'TestResult',
    component: () => import('@/views/Test/result.vue'),
  },
  {
    path: '/bank-test-index/:type',
    name: 'BankTestIndex',
    component: () => import('@/views/BankTest/index.vue'),
  },
  {
    path: '/bank-test/:type',
    name: 'BankTest',
    component: () => import('@/views/BankTest/test.vue'),
  },
  {
    path: '/bank-collect/:type',
    name: 'BankCollect',
    component: () => import('@/views/MyBank/BankCollect.vue'),
  },
  {
    path: '/bank-collect-list/:type/:bankId',
    name: 'BankCollectList',
    component: () => import('@/views/MyBank/BankCollectList.vue'),
  },
  {
    path: '/bank-correction',
    name: 'BankCorrection',
    component: () => import('@/views/MyBank/index.vue'),
  },
  {
    path: '/bank-exam/:type', // query: bankId questionId
    name: 'BankExam',
    component: () => import('@/views/MyBank/BankExam.vue'),
  },
  {
    path: '/exam-page',
    name: 'ExamPage',
    component: () => import('@/views/MyExam/ExamPage.vue'),
  },
  {
    path: '/exam-test',
    name: 'ExamTest',
    component: () => import('@/views/MyExam/ExamTest.vue'),
  },
  {
    path: '/exam-result',
    name: 'ExamResult',
    component: () => import('@/views/MyExam/ExamResult.vue'),
  },
  {
    path: '/my-attachment',
    name: 'MyAttachment',
    component: () => import('@/views/PersonPage/MyAttachment.vue'),
  },
  {
    path: '/completed-course',
    name: 'CompletedCourse',
    component: () => import('@/views/PersonPage/CompletedCourse.vue'),
  },
  {
    path: '/my-scores',
    name: 'MyScores',
    component: () => import('@/views/PersonPage/MyScores.vue'),
  },
];
const router = createRouter({
  history: createWebHashHistory(),
  routes,
});

router.beforeEach((to, from, next) => {
  if (!sessionStorage.getItem('__token__')) {
    console.log('-------beforeRouteEnter-------------');
    let _platform = window.sessionStorage.getItem('__platform__');
    console.log(`平台标识：${_platform === platform.android ? '安卓' : _platform === platform.ios ? 'ios' : '未知设备'}==>${_platform}`);
    const params = JSON.stringify({
      useTitleBar: false,
      objName: nameSpace, // objName app 调用的web方法命名空间，用于隔离window全局方法
    });
    if (_platform + '' === platform.ios) {
      window.webkit.messageHandlers.client_isAlready.postMessage(params);
      window.webkit.messageHandlers.client_getStorage.postMessage(
        JSON.stringify({
          key: 'JS_storage',
        }),
      );
    } else if (_platform + '' === platform.android) {
      window.DDBESOFFICE.client_isAlready(params);
    }
    Bus.on('token_save', () => {
      next();
    });
  } else {
    next();
  }
});

export default router;
