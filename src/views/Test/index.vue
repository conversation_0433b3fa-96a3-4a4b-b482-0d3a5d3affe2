<script setup>
import { useRoute, useRouter } from 'vue-router';

const router = useRouter();
const route = useRoute();

/** 获取数据 */

const { planId, courseId, hourId } = route.params;

let data = sessionStorage.getItem('__test_info__');
data = data ? JSON.parse(data) : null;

console.log(data);

if (!data) {
  handleClickBack();
}

/** 返回 */

function handleClickBack() {
  router.back();
}

/** 跳转进入考试页面 */

function handleClickStart() {
  router.push(`/test-index/test?planId=${planId}&courseId=${courseId}&hourId=${hourId}`);
}
</script>

<template>
  <div class="h-full flex flex-col bg-white">
    <van-nav-bar title="随堂考试" left-text="返回" left-arrow @click-left="handleClickBack" />
    <div class="flex-1 flex flex-col items-center px-[50px]">
      <div class="w-full my-[50px]">
        <van-row class="h-[80px] w-full">
          <van-col span="6" class="leading-[80px]">
            <span class="text-[32px] font-font font-normal text-descColor">题目数量: </span>
          </van-col>
          <van-col span="18" class="leading-[80px]">
            <span class="text-[32px] font-font font-normal text-simple">{{ data.questionCount }}</span>
          </van-col>
        </van-row>
        <van-row class="h-[80px] w-full">
          <van-col span="6" class="leading-[80px]">
            <span class="text-[32px] font-font font-normal text-descColor">合格: </span>
          </van-col>
          <van-col span="18" class="leading-[80px]">
            <span class="text-[32px] font-font font-normal text-simple"
              >{{ Math.ceil(data.questionCount * (data.qualificationRate / 100)) }}/{{ data.questionCount }}</span
            >
          </van-col>
        </van-row>
        <van-row class="h-[80px] w-full">
          <van-col span="6" class="leading-[80px]">
            <span class="text-[32px] font-font font-normal text-descColor">提交次数: </span>
          </van-col>
          <van-col span="18" class="leading-[80px]">
            <span class="text-[32px] font-font font-normal text-simple">{{ data.loopCount - 1 }}/{{ data.maxLoopCount }}</span>
          </van-col>
        </van-row>
      </div>
      <div @click="handleClickStart()" class="w-[580px] h-[100px] bg-blue rounded-[15px] leading-[100px] text-center">
        <span class="text-[34px] font-font font-medium text-white">进入考试</span>
      </div>
    </div>
  </div>
</template>
