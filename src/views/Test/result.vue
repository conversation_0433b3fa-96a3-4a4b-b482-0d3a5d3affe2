<script setup>
import { computed, ref } from 'vue';
import { onBeforeRouteLeave, useRoute, useRouter } from 'vue-router';
import { parseTimeToFullDate } from '@/utils';
import { TestApi } from '@/api';

const router = useRouter();
const route = useRoute();

const { planId, courseId, hourId } = route.query;

onBeforeRouteLeave((to, from, next) => {
  if (to.name === 'TestIndex') {
    sessionStorage.setItem('__test_paper_index__', JSON.stringify(paperIndex.value));
    sessionStorage.setItem('__test_paper_result__', JSON.stringify(paperResults.value));
    sessionStorage.setItem('__test_analysis_info__', JSON.stringify(allQuestionList.value));
  } else {
    sessionStorage.removeItem('__test_paper_index__');
    sessionStorage.removeItem('__test_paper_result__');
    sessionStorage.removeItem('__test_analysis_info__');
  }
  next();
});

/**
 * 获取试卷列表
 */

const paperResults = ref([]);
const paperIndex = ref(0);
const titleOptions = ref([]);
const currentPaper = ref({});

async function getTestPaperResult() {
  try {
    const res = await TestApi.getTestResult(courseId, hourId, planId);
    if (res.code === 0) {
      let { hourTestPapers, wrongRecords } = res.data;
      hourTestPapers = Object.values(hourTestPapers).map((paper) => {
        const wrongRecord = wrongRecords[paper.id];
        if (wrongRecord && wrongRecord.length > 0) {
          const wrongRecordMap = {};
          wrongRecord.forEach((item) => {
            wrongRecordMap[item.questionId] = item;
          });
          paper.questionList.forEach((question) => {
            const wrongQuestion = wrongRecordMap[question.id];
            if (wrongQuestion) {
              question.wrongAnswers = wrongQuestion.wrongAnswers;
            }
          });
        }
        return paper;
      });
      setPaper(hourTestPapers, hourTestPapers.length - 1);
    } else {
      console.log(res.msg);
    }
  } catch (e) {
    console.log('获取考试结果失败，请稍后重试', e);
  }
}

function setPaper(papers, index) {
  paperResults.value = papers;
  paperIndex.value = index;
  currentPaper.value = papers[paperIndex.value];
  const options = [];
  papers.forEach((item, index) => {
    options.push({
      text: `第${index + 1}次考试结果`,
      value: index,
    });
  });
  titleOptions.value = options;
}

function handleTitleChange(val) {
  paperIndex.value = val;
  currentPaper.value = paperResults.value[paperIndex.value];
}

/** 返回 */
function handleClickBack() {
  router.back();
}

/** 筛选 */

const value1 = ref(0);
const option1 = [
  { text: '全部题目', value: 0 },
  { text: '只看错的', value: 1 },
  { text: '只看对的', value: 2 },
];

const allQuestionList = computed(() => {
  return currentPaper.value.questionList || [];
});

const wrongQuestionList = computed(() => {
  return allQuestionList.value.filter((item) => item.wrongAnswers);
});

const rightQuestionList = computed(() => {
  return allQuestionList.value.filter((item) => !item.wrongAnswers);
});

const questionList = computed(() => {
  switch (value1.value) {
    case 0:
      return allQuestionList.value;
    case 1:
      return wrongQuestionList.value;
    case 2:
      return rightQuestionList.value;
    default:
      return allQuestionList.value;
  }
});

/** 查看解析 */

function handleClickResult() {
  router.push('/test-index/analysis');
}

(function init() {
  const paperIndexStorage = sessionStorage.getItem('__test_paper_index__');
  const paperResultsStorage = sessionStorage.getItem('__test_paper_result__');
  if (paperResultsStorage) {
    setPaper(JSON.parse(paperResultsStorage), paperIndexStorage ? Number(paperIndexStorage) : paperResults.value.length - 1);
    return;
  }
  getTestPaperResult();
})();
</script>

<template>
  <div class="flex flex-col h-full overflow-hidden">
    <van-nav-bar title="考试结果" left-text="返回" left-arrow @click-left="handleClickBack">
      <template #title>
        <van-dropdown-menu active-color="#0168FD">
          <van-dropdown-item v-model="paperIndex" :options="titleOptions" @change="handleTitleChange" />
        </van-dropdown-menu>
      </template>
    </van-nav-bar>
    <!--  成绩  -->
    <div class="flex flex-col justify-center mb-[16px] h-[160px] bg-white pl-[40px]">
      <div class="flex mb-[10px] text-[28px] font-font font-normal text-simple">
        <span>正确题数: {{ rightQuestionList.length }}道</span>
        <tag v-if="currentPaper.isPass" class="ml-1" message="合格" type="success" />
        <tag v-else class="ml-1" message="不合格" type="danger"></tag>
      </div>
      <div class="">
        <span class="text-[24px] font-font font-normal text-descColor">提交日期 {{ parseTimeToFullDate(currentPaper.finishTime, '-') }}</span>
      </div>
    </div>
    <!--  结果  -->
    <div class="flex-1 flex flex-col bg-white overflow-auto">
      <van-dropdown-menu active-color="#0168FD">
        <van-dropdown-item v-model="value1" :options="option1" />
      </van-dropdown-menu>
      <div class="flex-1 p-[24px] overflow-auto">
        <div v-for="(item, index) in questionList" :key="index" class="flex py-[23px] border-b border-border last:border-b-0">
          <div class="ml-[11px] mr-[21px]">
            {{ item.wrongAnswers ? '❌' : '✅' }}
          </div>
          <span class="text-[28px] font-font font-normal text-simple my-text-one-line-overflow">{{ item.stem }}</span>
        </div>
      </div>
    </div>
    <!--  查看解析  -->
    <div class="flex justify-center pt-[10px] h-[150px] bg-white">
      <div @click="handleClickResult" class="w-[580px] h-[100px] bg-blue text-center leading-[100px] rounded-[15px]">
        <span class="text-[34px] font-font font-medium text-white">查看解析</span>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
