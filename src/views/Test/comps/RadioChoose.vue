<script setup>
import { computed, ref, watch } from 'vue';

const props = defineProps({
  list: {
    type: Array,
    required: true,
  },
  answer: {
    type: String,
    required: true,
    default: '',
  },
  mode: {
    type: String,
    default: 'write',
  },
  wrongAnswers: {
    type: Array,
    default: undefined,
  },
});
const emit = defineEmits(['change']);

const activeIndex = ref('');

watch(
  () => props.answer,
  (val) => {
    activeIndex.value = val;
  },
  { immediate: true, deep: true },
);

function handleChange(id) {
  if (props.mode === 'read') return;
  activeIndex.value = id;
  emit('change', props.list[0].questionId, activeIndex.value);
}
</script>

<template>
  <div class="flex flex-col">
    <div
      v-for="item in list"
      :key="item.tag"
      class="py-[18px] pl-[80px] pr-[30px] flex items-center rounded-[8px]"
      :class="{
        'bg-bgBlue': props.mode === 'write' && activeIndex === item.id,
      }"
      @click="handleChange(item.id)"
    >
      <van-row class="w-full">
        <van-col span="2">
          <div
            class="flex justify-center items-center border border-border rounded-[50%] w-[45px] h-[45px]"
            :style="{
              'border-color': props.mode === 'write' && activeIndex === item.id ? '#0168FD' : '#E4E9EF',
            }"
          >
            <div v-if="props.mode === 'read' && !props.wrongAnswers && activeIndex === item.id">✅</div>
            <div v-else-if="props.mode === 'read' && props.wrongAnswers && props.wrongAnswers[0] === item.id">❌</div>
            <span
              v-else
              class="text-[30px] font-font font-normal text-simple"
              :style="{
                color: props.mode === 'write' && activeIndex === item.id ? '#0168FD' : '#000',
              }"
              >{{ item.tag }}</span
            >
          </div>
        </van-col>
        <van-col span="22" class="pl-[15px]"
          ><span
            class="text-[30px] font-font font-normal text-simple"
            :style="{
              color: props.mode === 'write' && activeIndex === item.id ? '#0168FD' : '#000',
            }"
            >{{ item.optionText }}</span
          ></van-col
        >
      </van-row>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
