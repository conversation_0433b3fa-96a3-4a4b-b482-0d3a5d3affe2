<script setup>
const props = defineProps({
  type: {
    type: String,
    required: true,
  },
});

const types = {
  SINGLE: '单选',
  MULTI: '多选',
  T_OR_F: '判断',
};
</script>

<template>
  <div class="float-left flex items-center justify-center mx-[10px] w-[62px] h-[38px] bg-bgBlue rounded-[8px]">
    <span class="text-[22px] font-font font-normal text-blue">{{ types[props.type] }}</span>
  </div>
</template>

<style scoped lang="scss"></style>
