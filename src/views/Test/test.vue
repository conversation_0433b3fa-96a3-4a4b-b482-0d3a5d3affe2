<script setup>
import { ref } from 'vue';
import RadioCheckbox from '@/views/Test/comps/RadioChoose.vue';
import Tag from '@/views/Test/comps/tag.vue';
import CheckBoxChoose from '@/views/Test/comps/CheckBoxChoose.vue';
import JudgeChoose from '@/views/Test/comps/JudgeChoose.vue';
import { useRoute, useRouter } from 'vue-router';
import { TestApi } from '@/api';
import { showToast } from 'vant';
import { showConfirmDialog } from 'vant';
import 'vant/es/toast/style';

const router = useRouter();
const route = useRoute();

const { mode } = route.params;
const { planId, courseId, hourId } = route.query;

/** 获取试题信息 */

let data;

if (mode === 'test') {
  data = sessionStorage.getItem('__test_info__');
} else if (mode === 'analysis') {
  data = sessionStorage.getItem('__test_analysis_info__');
}

if (!data) {
  handleClickBack();
}

data = JSON.parse(data);

console.log(123, data);

const list = ref([]);

if (mode === 'test') {
  list.value = data.questionList;
} else if (mode === 'analysis') {
  list.value = data;
}

/** 获取正确答案的tag */

function getAnswerTag(data) {
  let result = '';
  switch (data.type) {
    case 'SINGLE':
      var rightOption = data.questionOptions.find((item) => item.id === data.answer);
      rightOption && (result = rightOption.tag);
      break;
    case 'MULTI':
      var rightOptions = data.questionOptions.filter((item) => data.answer.includes(item.id)).map((item) => item.tag);
      result = rightOptions.join(',');
      break;
    case 'T_OR_F':
      result = data.answer === 'T' ? 'A' : 'B';
      break;
  }
  return result;
}

/** 返回 */
function handleClickBack() {
  router.back();
}

/** 切换试题 */

const index = ref(0);

function handleChangeToLast() {
  if (index.value > 0) {
    index.value--;
  }
}

function handleChangeToNext() {
  if (index.value < list.value.length - 1) {
    index.value++;
  }
}

const showPopup = ref(false);

/** 设置答案 */

function handleAnswerChange(questionId, value) {
  console.log(questionId, value);
  const question = list.value.find((item) => item.id === questionId);
  if (question) {
    question.answer = value;
  } else {
    console.log('未找到试题', questionId, value);
  }
  console.log(list.value);
}

/** 提交 */
function handleClickSubmit() {
  if (list.value.some((item) => !item.answer)) {
    showToast('您尚未完成全部试题，请作答后再进行提交');
    return;
  }

  showConfirmDialog({
    message: '提交后将立即生成考试成绩，确认提交吗？',
  })
    .then(async () => {
      try {
        const submitList = list.value.map((item) => {
          return {
            answers: item.answer.split(','),
            questionId: item.id,
            type: item.type,
          };
        });
        const res = await TestApi.submitCourseHourQuestion({
          paperId: data.id,
          questionSubmitList: submitList,
        });
        if (res.code === 0) {
          router.go(-2);
          setTimeout(() => {
            router.push(`/test-result?planId=${planId}&courseId=${courseId}&hourId=${hourId}`);
          }, 100);
        } else {
          console.log(res.msg);
        }
      } catch (e) {
        console.log('提交失败，请稍后重试', e);
      }
    })
    .catch(() => {
      // on cancel
    });
}
</script>

<template>
  <div class="h-full flex flex-col">
    <van-nav-bar :title="`试题 ${index + 1} / ${list.length}`" left-text="返回" left-arrow @click-left="handleClickBack">
      <template #right>
        <span v-if="mode === 'test'" class="text-blue" @click="handleClickSubmit">提交</span>
      </template>
    </van-nav-bar>
    <div class="flex-1 overflow-auto">
      <!--  题干及选项-->
      <div class="bg-white py-[30px]">
        <div class="flex px-[30px] pb-[20px]">
          <div class="w-[50px] text-right">{{ index + 1 }}.</div>
          <div class="flex-1">
            <tag :type="list[index].type" />
            <div class="w-full text-[30px] font-font font-normal text-simple">
              {{ list[index].stem }}
            </div>
          </div>
        </div>
        <radio-checkbox
          v-if="list[index].type === 'SINGLE'"
          :list="list[index].questionOptions"
          :answer="list[index].answer"
          :mode="mode === 'test' ? 'write' : 'read'"
          :wrong-answers="list[index].wrongAnswers"
          @change="handleAnswerChange"
        />
        <check-box-choose
          v-if="list[index].type === 'MULTI'"
          :list="list[index].questionOptions"
          :answer="list[index].answer"
          :mode="mode === 'test' ? 'write' : 'read'"
          :wrong-answers="list[index].wrongAnswers"
          @change="handleAnswerChange"
        />
        <judge-choose
          v-if="list[index].type === 'T_OR_F'"
          :question-id="list[index].id"
          :answer="list[index].answer"
          :mode="mode === 'test' ? 'write' : 'read'"
          :wrong-answers="list[index].wrongAnswers"
          @change="handleAnswerChange"
        />
      </div>
      <!--   答案与解析  -->
      <div v-if="mode === 'analysis'" class="pl-[80px] pt-[32px] pr-[44px] pb-[30px]">
        <div class="mb-[30px] text-[35px] font-font font-normal">
          <span class="text-green" v-show="!list[index].wrongAnswers">正确答案 {{ getAnswerTag(list[index]) }}</span>
          <span class="text-orange" v-show="list[index].wrongAnswers">正确答案 {{ getAnswerTag(list[index]) }}</span>
        </div>
        <div class="flex text-[30px] font-font">
          <div class="w-[100px] font-medium text-titleColor">解析:</div>
          <div class="font-normal text-simple">
            {{ list[index].analysis || '暂无解析' }}
          </div>
        </div>
      </div>
    </div>
    <!--  导航  -->
    <div class="flex items-center h-[130px] w-full bg-white">
      <!--  导航：第一题且不是最后一题  -->
      <div v-show="index === 0 && index !== list.length - 1" class="flex w-full h-[80px]">
        <div @click="handleChangeToNext" class="w-1/2 tab-item tab-item-border">下一题</div>
        <div @click="showPopup = true" class="w-1/2 tab-item">全部题目</div>
      </div>
      <!--  导航：中间题目  -->
      <div v-show="index > 0 && index < list.length - 1" class="flex w-full h-[80px]">
        <div @click="handleChangeToLast" class="w-1/3 tab-item tab-item-border">上一题</div>
        <div @click="handleChangeToNext" class="w-1/3 tab-item tab-item-border">下一题</div>
        <div @click="showPopup = true" class="w-1/3 tab-item">全部题目</div>
      </div>
      <!--  导航：最后一题且不是第一题  -->
      <div v-show="index === list.length - 1 && index !== 0" class="flex w-full h-[80px]">
        <div @click="handleChangeToLast" class="w-1/2 tab-item tab-item-border">上一题</div>
        <div @click="showPopup = true" class="w-1/2 tab-item">全部题目</div>
      </div>
      <!--  导航：第一题且最后一题  -->
      <div v-show="index === list.length - 1 && index === 0" class="flex w-full h-[80px]">
        <div @click="showPopup = true" class="w-full tab-item">全部题目</div>
      </div>
    </div>
    <!--  试题选择弹窗  -->
    <van-popup :show="showPopup" position="bottom" :style="{ height: '60%', overflow: 'auto' }" @click-overlay="showPopup = false">
      <div class="grid grid-cols-7 justify-items-center py-[50px] px-[20px]">
        <div
          v-for="(item, i) in list"
          :key="i"
          @click="
            () => {
              index = i;
              showPopup = false;
            }
          "
          class="flex justify-center items-center mb-[20px] border border-border rounded-[4px] w-[80px] h-[80px]"
          :style="{
            'border-color': mode === 'test' && list[i].answer ? '#0168FD' : '#E4E9EF',
          }"
        >
          <div v-if="mode === 'analysis' && !list[i].wrongAnswers">✅</div>
          <div v-else-if="mode === 'analysis' && list[i].wrongAnswers">❌</div>
          <span
            v-else
            class="text-[35px] font-font font-normal text-simple"
            :style="{
              color: list[i].answer ? '#0168FD' : '#000',
            }"
            >{{ i + 1 }}</span
          >
        </div>
      </div>
    </van-popup>
  </div>
</template>

<style scoped lang="scss">
.tab-item {
  @apply h-full text-center leading-[80px];
}

.tab-item-border {
  @apply border-r border-border;
}
</style>
