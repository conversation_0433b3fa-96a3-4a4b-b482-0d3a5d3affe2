<script setup>
import CourseListItem from '@/views/Details/comps/CourseListItem.vue';
import CourseFileItem from '@/views/Details/comps/CourseFileItem.vue';
import CourseChapterList from '@/views/Details/comps/CourseChapterList.vue';
import { useRoute, useRouter } from 'vue-router';
import { DetailApi, TestApi, ProfileApi } from '@/api';
import { computed, ref } from 'vue';
import { appBack } from '@/utils/index';
import { showDialog, showConfirmDialog } from 'vant';
import CourseExamItem from '@/views/Details/comps/CourseExamItem.vue';
import { showToast } from 'vant';
import 'vant/es/toast/style';
import Collect from '@/assets/profile/collect.svg';
import CollectActive from '@/assets/profile/collect-active.svg';
import dayjs from 'dayjs';
import Tag from '@/components/Tag.vue';
import Tabs from '@/components/Tabs.vue';

const route = useRoute();
const router = useRouter();
const { courseId, planId } = route.params;
const { isFinish, isHome } = route.query;
const planIds = ref(['']);

/**
 * 水印文字
 */

const userInfo = sessionStorage.getItem('__user__') && JSON.parse(sessionStorage.getItem('__user__'));

// 根据 isHome 判断是从首页进入，从首页进入详情弹出弹窗提示

if (isHome) {
  showDialog({
    message: '首页课程中不包含计划内所安排的随堂考试',
  }).then(() => {
    // on close
  });
}

/** 页面初始化 **/

const courseDetail = ref({});
getDetailById();

// 获取课程详情
async function getDetailById() {
  try {
    const res = await DetailApi.getCourseDetailById(courseId, planId);
    if (res.code === 0) {
      courseDetail.value = res.data;
      const planIdArr = courseDetail.value.course.planIds;
      planIds.value = planIdArr.length > 0 ? ['', ...planIdArr] : [''];
      sessionStorage.setItem('__course_planId__', JSON.stringify(planIds.value));
    } else {
      console.log(res.msg || res.message);
    }
  } catch (e) {
    console.log('请求课程详情失败， 请稍后重试', e);
    router.back();
  }
}

/** 课程详情页 tabs **/

const tabActive = ref('0');

const containerRef = ref(null);
const infoRef = ref(null);
const catalogRef = ref(null);
const examRef = ref(null);
const annexRef = ref(null);

const tabOptions = [
  { label: '简介', value: '0' },
  { label: '目录', value: '1' },
  { label: '附件', value: '3' },
];
if (planId) {
  tabOptions.splice(2, 0, { label: '考试', value: '2' });
}

function handleTabChange() {
  console.log(123);
  
  let targetRef;
  switch (tabActive.value) {
    case '0':
      targetRef = infoRef.value;
      break;
    case '1':
      targetRef = catalogRef.value;
      break;
    case '2':
      targetRef = examRef.value;
      break;
    case '3':
      targetRef = annexRef.value;
      break;
  }
  if (targetRef) {
    containerRef.value.scrollTo({
      top: targetRef.offsetTop - containerRef.value.offsetTop,
      behavior: 'smooth',
    });
  }
}

/** 课时查看与文件预览 **/

// 获取文件预览 url
function handleMediaPlay(isFile, file) {
  if (isFinish !== undefined) {
    showConfirmDialog({
      message: '该培训已经结束，现在观看不会改变当前的成绩',
      confirmButtonText: '继续观看',
      confirmButtonColor: '#326FFF',
    })
      .then(() => {
        play(isFile, file);
      })
      .catch(() => {
        // on cancel
      });
  } else {
    play(isFile, file);
  }
}

async function play(isFile, file) {
  try {
    const rid = isFile ? file.id : file.rid;
    const name = isFile ? file.name : file.title;
    const res = await DetailApi.getFileUrl(rid);
    if (res.code === 0) {
      const inPlan = courseDetail.value.planIds.length > 0;
      if (file.type === 'VIDEO') {
        sessionStorage.setItem('__player_url__', res.data);
        isFile ? router.push('/simple-player') : router.push(`/player/${courseId}/${file.id}/${planId}?inPlan=${inPlan}`);
      } else if (file.type === 'IMAGE') {
        sessionStorage.setItem('__preview_image_url__', res.data);
        isFile ? router.push(`/image-preview/${name}`) : router.push(`/image-preview/${name}/${courseId}/${file.id}/${planId}?inPlan=${inPlan}`);
      } else {
        sessionStorage.setItem('__preview_url__', res.data);
        isFile ? router.push(`/file-preview/${name}`) : router.push(`/file-preview/${name}/${courseId}/${file.id}/${planId}?inPlan=${inPlan}`);
      }
    } else {
      console.log(res.msg || res.message);
    }
  } catch (e) {
    console.log('请求课时失败，请稍后重试', e);
  }
}

/** 章节与课时进度 **/

// 判断课时是否完成
function checkHourIsFinish(hourId) {
  return computed(() => {
    const hourRecord = courseDetail.value.learn_hour_records[String(hourId)];
    return hourRecord ? hourRecord.isFinished : false;
  });
}

// 判断课程考试是否合格
function checkTestMode(hourId) {
  if (!planId) return 0; // 如果是从首页进入则不显示状态
  const learnStatus = checkLearnStatus(hourId);
  const hasTest = courseDetail.value.tests[String(hourId)];
  const testRecord = courseDetail.value.testRecords[String(hourId)];
  if (!hasTest) {
    // 没有考试
    return 0;
  } else if (learnStatus !== 2) {
    // 课时未完成
    return 1;
  } else if (!testRecord) {
    // 有考试但但还没考
    return 2;
  } else if (testRecord && testRecord.isPass) {
    // 有考试且已经考过
    return 3;
  } else if (testRecord && !testRecord.isPass) {
    // 有考试且还没考过
    return 4;
  } else {
    return 0;
  }
}

// 判断课时观看状态
function checkLearnStatus(hourId) {
  const hourRecord = courseDetail.value.learn_hour_records[String(hourId)];
  if (!hourRecord) {
    return 0;
  } else if (hourRecord.loopCount === 0) {
    return 1;
  } else {
    return 2;
  }
}

// 跳转到考试结果页
function handleGoResult(hourId) {
  router.push(`/test-result?planId=${planId}&courseId=${courseId}&hourId=${hourId}`);
}

// 跳转到考试页面
async function handleGoTest(hourId) {
  try {
    const res = await TestApi.getCourseHourQuestion(courseId, hourId, planId);
    if (res.code === 0) {
      sessionStorage.setItem('__test_info__', JSON.stringify(res.data));
      router.push(`/test/${courseId}/${hourId}/${planId}`);
    } else {
      console.log(res.msg || res.message);
      showToast(res.msg);
    }
  } catch (e) {
    console.log('获取考试信息失败，请稍后重试', e);
  }
}

const examChapters = computed(() => {
  const chapters = [];
  courseDetail.value.chapters.map((chapter) => {
    const hours = courseDetail.value.hours[String(courseId)]
      .filter((hour) => hour.chapter_id === chapter.id)
      .filter((hour) => Object.keys(courseDetail.value.tests).includes(String(hour.id)));
    hours.length > 0 && chapters.push({ chapter, hours });
  });
  return chapters;
});

/** 收藏 **/

const loading = ref(false);

// 收藏
async function addFavoriteCourse(id) {
  try {
    loading.value = true;
    const res = await ProfileApi.addFavorite([id]);
    if (res.code !== 0) throw new Error(res.msg);
    showToast('收藏成功');
    getDetailById();
  } catch (error) {
    showToast('收藏失败');
  } finally {
    loading.value = false;
  }
}

// 取消收藏
async function cancelFavoriteCourse(id) {
  showConfirmDialog({
    title: '确定取消收藏吗？',
  })
    .then(async () => {
      try {
        loading.value = true;
        const res = await ProfileApi.cancelFavorite([id]);
        if (res.code !== 0) throw new Error(res.msg);
        showToast('取消收藏成功');
        getDetailById();
      } catch (error) {
        showToast('取消收藏失败');
      } finally {
        loading.value = false;
      }
    })
    .catch(() => {
      // on cancel
    });
}
</script>

<template>
  <div class="h-full flex flex-col bg-grey overflow-hidden">
    <van-watermark :content="userInfo.name + userInfo.mobile" :gap-x="30" :gap-y="50" :opacity="0.5" />

    <van-nav-bar title="课程详情" left-arrow left-text="返回" @click-left="appBack(router, route)">
      <template #right>
        <img v-show="!courseDetail?.course?.isFavorite" :src="Collect" @click="addFavoriteCourse(courseDetail.course.id)" />
        <img v-show="courseDetail?.course?.isFavorite" :src="CollectActive" @click="cancelFavoriteCourse(courseDetail.course.id)" />
      </template>
    </van-nav-bar>

    <div v-if="Object.keys(courseDetail).length > 0" ref="containerRef" class="flex-1 overflow-auto">
      <!--  课程信息展示 -->
      <div class="relative p-[32px] pb-[16px] bg-white">
        <img class="w-[686px] h-[372px] rounded-[16px]" :src="courseDetail.course.thumb" alt="" />
        <div class="mt-[32px] text-[32px] font-medium leading-normal text-titleColor my-text-two-lines-overflow">
          {{ courseDetail.course.title }}
        </div>
        <div v-if="courseDetail.course.planVo" class="flex items-center gap-[16px] mt-[16px]">
          <tag v-if="dayjs(courseDetail.course.planVo.startTime).isBefore(dayjs())" type="success" message="已开课"></tag>
          <span class="text-desc text-[26px]"
            >{{ dayjs(courseDetail.course.planVo.startTime).format('YYYY.MM.DD') }}-{{
              dayjs(courseDetail.course.planVo.endTime).format('YYYY.MM.DD')
            }}</span
          >
        </div>
        <div class="flex items-center gap-[8px] mt-[16px]">
          <img class="w-[28px] h-[28px]" src="@/assets/profile/courseCategory.svg" alt="" />
          <div class="text-[26px] font-normal text-desc">{{ courseDetail.course.categoryVo.name }}</div>
        </div>
        <div class="flex items-center gap-[8px] mt-[16px]">
          <img class="w-[28px] h-[28px]" src="@/assets/profile/courseUser.svg" alt="" />
          <div class="text-[26px] font-normal text-desc">{{ courseDetail.course.teacher }}</div>
        </div>
      </div>

      <tabs v-model="tabActive" :show-border="true" :tabs="tabOptions" @change="handleTabChange" class="mt-[16px] bg-white"></tabs>

      <!--  课程描述  -->
      <div ref="infoRef" class="bg-white p-[32px]">
        <div class="text-[28px] font-medium text-titleColor">课程信息</div>
        <div class="mt-[16px] text-[28px] font-normal text-simple">
          <div v-if="courseDetail.course.shortDesc.length > 0">
            <div v-for="(item, index) in courseDetail.course.shortDesc.split('\n')" :key="index">
              {{ item }}
            </div>
          </div>
          <div v-else>无</div>
        </div>
      </div>

      <!--  课程目录-课时  -->
      <div
        ref="catalogRef"
        v-if="courseDetail.chapters.length === 0 && courseDetail.hours[String(courseId)].length > 0"
        class="w-full mt-[16px] p-[32px] bg-white"
      >
        <div class="text-[28px] font-medium text-titleColor">课程目录</div>
        <div v-for="(hour, index) in courseDetail.hours[String(courseId)]" :key="hour.id" class="mt-[16px]">
          <course-list-item
            class="bg-grey rounded-[12px]"
            :index="index"
            :hour="hour"
            :isFinish="checkHourIsFinish(hour.id).value"
            :learn-mode="checkLearnStatus(hour.id)"
            @play="handleMediaPlay(false, hour)"
          ></course-list-item>
        </div>
      </div>

      <!-- 课程目录-章节  -->
      <div ref="catalogRef" v-if="courseDetail.chapters.length > 0" class="w-full mt-[16px] p-[32px] bg-white">
        <div class="text-[28px] font-medium text-titleColor">课程目录</div>
        <div v-for="(chapter, index) in courseDetail.chapters" :key="chapter.id" class="mt-[16px]">
          <course-chapter-list
            :index="index"
            :chapter="chapter"
            :hoursLength="courseDetail.hours[String(courseId)].filter((hour) => hour.chapter_id === chapter.id).length"
          >
            <div
              class="flex flex-col items-center"
              v-for="(hour, i) in courseDetail.hours[String(courseId)].filter((hour) => hour.chapter_id === chapter.id)"
              :key="hour.id"
            >
              <course-list-item
                class="w-[611px] bg-white"
                :index="i"
                :hour="hour"
                :is-finish="checkHourIsFinish(hour.id).value"
                :learn-mode="checkLearnStatus(hour.id)"
                @play="handleMediaPlay(false, hour)"
              />
            </div>
          </course-chapter-list>
        </div>
      </div>

      <!--  课程目录-无  -->
      <div
        ref="catalogRef"
        v-if="courseDetail.chapters.length === 0 && courseDetail.hours[String(courseId)].length === 0"
        class="w-full mt-[16px] p-[32px] bg-white"
      >
        <div class="text-[28px] font-medium text-titleColor">课程目录</div>
        <van-empty class="mt-[16px]" description="未上传课程" />
      </div>

      <!--  考试目录-课时 -->
      <div
        ref="examRef"
        v-if="planId && courseDetail.chapters.length === 0 && courseDetail.hours[String(courseId)].length > 0"
        class="w-full mt-[16px] p-[32px] bg-white"
      >
        <div class="text-[28px] font-medium text-titleColor">考试列表</div>
        <div
          v-for="(hour, index) in courseDetail.hours[String(courseId)].filter((hour) => Object.keys(courseDetail.tests).includes(String(hour.id)))"
          :key="hour.id"
          class="mt-[16px]"
        >
          <course-exam-item
            class="bg-grey rounded-[12px]"
            :index="index"
            :hour="hour"
            :test-mode="checkTestMode(hour.id)"
            :test-record="courseDetail.testRecords[String(hour.id)]"
            @go-test="handleGoTest(hour.id)"
            @go-result="handleGoResult(hour.id)"
          />
        </div>
      </div>

      <!-- 考试目录-章节 -->
      <div ref="examRef" v-if="planId && examChapters.length > 0" class="w-full mt-[16px] p-[32px] bg-white">
        <div class="text-[28px] font-medium text-titleColor">考试列表</div>
        <div v-for="(chapter, index) in examChapters" :key="chapter.id" class="mt-[16px]">
          <course-chapter-list :index="index" :chapter="chapter.chapter" :hoursLength="chapter.hours.length" desc="场考试">
            <div class="flex flex-col items-center" v-for="(hour, i) in chapter.hours" :key="hour.id">
              <course-exam-item
                class="w-[611px] bg-white"
                :index="i"
                :hour="hour"
                :test-mode="checkTestMode(hour.id)"
                :test-record="courseDetail.testRecords[String(hour.id)]"
                @go-test="handleGoTest(hour.id)"
                @go-result="handleGoResult(hour.id)"
              />
            </div>
          </course-chapter-list>
        </div>
      </div>

      <!--  课程附件  -->
      <div ref="annexRef" class="w-full mt-[16px] p-[32px] bg-white">
        <div class="text-[28px] font-medium text-titleColor">附件</div>
        <div v-for="annex in courseDetail.annex_list" :key="annex.id" class="mt-[16px]" @click="handleMediaPlay(true, annex)">
          <course-file-item :data="annex" />
        </div>
        <div v-if="courseDetail.annex_list.length === 0">
          <van-empty class="mt-[16px]" description="未上传附件" />
        </div>
      </div>
    </div>
  </div>
</template>
