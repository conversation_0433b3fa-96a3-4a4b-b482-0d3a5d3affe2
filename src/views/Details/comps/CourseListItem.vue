<script setup>
import { formatTimeToMinutes, formatNumberToXX, formatKBToMB } from '@/utils/utils.js';

const props = defineProps({
  index: Number,
  hour: Object,
  isFinish: Boolean,
  learnMode: Number,
});

const emit = defineEmits(['play']);
</script>

<template>
  <div class="relative pl-[24px] pr-[32px] py-[18px] h-[121px]">
    <img v-if="props.isFinish" class="absolute left-[24px] top-[27px] w-[26px] h-[26px]" src="@/assets/comps/course-list/finish.png" alt="" />
    <img v-else class="absolute left-[24px] top-[27px] w-[24px] h-[24px]" src="@/assets/comps/course-list/unfinish.png" alt="" />
    <div class="ml-[40px] mr-[76px]">
      <div class="mb-[11px] text-[28px] font-font font-normal text-simple my-text-one-line-overflow">
        {{ formatNumberToXX(index + 1) }}.{{ props.hour.title }}
      </div>
      <div class="flex items-center">
        <div class="text-[24px] font-font font-normal text-descColor">
          {{
            props.hour.type === 'VIDEO' ? `时长: ${formatTimeToMinutes(props.hour.duration)}` : `大小: ${formatKBToMB(props.hour.resource.size)} MB`
          }}
        </div>
        <div v-if="props.learnMode === 0" class="tag-text text-blue ml-2">
          <span>未观看</span>
        </div>
        <div v-if="props.learnMode === 1" class="tag-text text-blue ml-2">
          <span>继续观看</span>
        </div>
        <div v-if="props.learnMode === 2" class="tag-text text-blue ml-2">
          <span>观看完成</span>
        </div>
      </div>
    </div>
    <img @click="emit('play')" src="@/assets/comps/course-list/play.png" class="absolute right-[32px] top-[38px] w-[44px] h-[44px]" alt="" />
  </div>
</template>

<style scoped>
.tag-text {
  @apply text-[22px] font-font font-normal;
}
</style>
