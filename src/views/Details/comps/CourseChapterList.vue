<script setup>
import { ref } from 'vue';

const props = defineProps({
  index: Number,
  chapter: Object,
  hoursLength: Number,
  desc: {
    type: String,
    default: '课时',
  },
});

const open = ref(false);
</script>

<template>
  <div>
    <div class="flex items-center py-[16px] px-[24px] rounded-[12px] bg-grey" @click="open = !open">
      <div class="flex-1">
        <div class=" text-[28px] font-medium leading-normal text-simple">第{{ props.index + 1 }}章-{{ chapter.name }}</div>
        <div class="text-[24px] leading-normal text-descColor">共{{ props.hoursLength }}{{ desc }}</div>
      </div>
      <img v-show="!open" src="@/assets/comps/file-list/close.png" class="w-[27px]" alt="" />
      <img v-show="open" src="@/assets/comps/file-list/expand.png" class="w-[27px]" alt="" />
    </div>
    <transition name="accordion-transition">
      <div v-if="open" class="flex flex-col justify-center mt-[16px]">
        <slot></slot>
      </div>
    </transition>
  </div>
</template>

<style scoped lang="scss">
.slide-enter-active,
.slide-leave-active {
  transition: height 0.3s ease;
}

.slide-enter,
.slide-leave-to {
  height: 0;
  overflow: hidden;
}
</style>
