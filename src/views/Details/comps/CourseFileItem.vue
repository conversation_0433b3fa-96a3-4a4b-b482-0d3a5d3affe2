<script setup>
import { formatKBToMB } from '@/utils/utils';

const props = defineProps({
  data: Object,
});

function getFileIconByType(type) {
  switch (type) {
    case 'mp4':
      return require('@/assets/comps/file-list/unnkown.png');
    case 'png':
    case 'jpg':
    case 'gif':
    case 'jpeg':
      return require('@/assets/comps/file-list/image.png');
    case 'pdf':
      return require('@/assets/comps/file-list/pdf.png');
    case 'doc':
    case 'docx':
    case 'dotx':
      return require('@/assets/comps/file-list/doc.png');
    case 'xls':
    case 'xlsx':
    case 'xlsb':
    case 'xlsm':
      return require('@/assets/comps/file-list/xls.png');
    case 'pptx':
    case 'ppsx':
    case 'pps':
    case 'pots':
    case 'ppsm':
      return require('@/assets/comps/file-list/ppt.png');
    default:
      return require('@/assets/comps/file-list/unnkown.png');
  }
}

function handlePreview(id) {
  console.log(id);
}
</script>

<template>
  <div @click="handlePreview(props.data.id)" class="relative pl-[32px] pr-[24px] py-[24px] mb-[8px] h-[121px] bg-grey rounded-[10px]">
    <img class="absolute left-[32px] top-[24px] w-[59px] h-[72px]" :src="getFileIconByType(props.data.extension)" alt="" />
    <div class="ml-[80px] mr-[32px]">
      <div class="text-[28px] font-font font-normal text-simple my-text-one-line-overflow">
        {{ props.data.name }}
      </div>
      <div class="text-[24px] font-font font-normal text-descColor">{{ formatKBToMB(props.data.size) }} MB</div>
    </div>
    <!--    <img-->
    <!--      src="@/assets/comps/file-list/download.png"-->
    <!--      class="absolute right-[24px] top-[44px] w-[32px] h-[32px]"-->
    <!--      alt=""-->
    <!--    />-->
  </div>
</template>
