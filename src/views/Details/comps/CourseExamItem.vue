<script setup>
import { showToast } from 'vant';
import { formatNumberToXX } from '@/utils/utils';
import Tag from '@/components/Tag.vue';

const props = defineProps({
  index: Number,
  hour: Object,
  testMode: Number,
  testRecord: Object,
});

const emit = defineEmits(['goTest', 'goResult']);

function goTest() {
  if (props.testMode === 1) {
    showToast('视频观看完成后方可考试');
    return;
  }
  emit('goTest');
}
</script>

<template>
  <div class="relative py-[18px]">
    <div @click="goTest" class="ml-[40px] mr-[76px]">
      <div class="mb-[11px] text-[28px] font-font font-normal text-simple my-text-one-line-overflow">
        {{ formatNumberToXX(index + 1) }}.{{ props.hour.title }}
      </div>
      <div class="flex items-center">
        <tag v-if="props.testMode === 1" @click="showToast('视频观看完成后方可考试')" message="未考试" type="default"></tag>
        <tag v-else-if="props.testMode === 2" @click="emit('goTest')" message="未考试" type="default"></tag>
        <tag v-else-if="props.testMode === 3" @click="emit('goResult')" message="合格" type="success"></tag>
        <tag v-else-if="props.testMode === 4" @click="emit('goResult')" message="不合格" type="danger" />
        <div v-if="testRecord && testRecord.isFinish" class="mx-2 text-[22px] text-blue">考试完成</div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
