<script setup>
import { onBeforeUnmount, onMounted, ref, nextTick } from 'vue';
import WPS from '@/assets/js/web-office-sdk-v1.1.8.es.js';
import { useRouter, useRoute } from 'vue-router';
import { PlayerApi, TestApi } from '@/api';

const router = useRouter();
const route = useRoute();

/**
 * 水印
 */

const userInfo = sessionStorage.getItem('__user__') && JSON.parse(sessionStorage.getItem('__user__'));

/** 页面初始化 **/

const url = sessionStorage.getItem('__preview_url__');

const { name, courseId, hourId, planId } = route.params;
const { inPlan } = route.query;
let ext = name.substring(name.lastIndexOf('.') + 1);
ext = ext.split('-')[0];

if (!url) {
  router.back();
} else {
  getFilePreInfo();
}

async function getFilePreInfo() {
  try {
    const res = await PlayerApi.getFileToken({ fileUrl: url, ext });
    if (res.code === 1) {
      await nextTick();
      initWps(res.data.token, res.data.wpsUrl);
    }
  } catch (e) {
    console.log('页面初始化失败，请稍后重试', e);
    router.back();
  }
}

onBeforeUnmount(() => {
  sessionStorage.removeItem('__preview_url__');
});

/**
 * 获取考试信息
 */

const hasTest = ref(false);

const getTestInfo = async () => {
  try {
    const res = await TestApi.getCourseHourQuestion(courseId, hourId, planId);
    if (res.code !== 0) {
      throw new Error(res.msg);
    }
    sessionStorage.setItem('__test_info__', JSON.stringify(res.data));
    hasTest.value = true;
  } catch (error) {
    console.log(error.message);
  }
};

onMounted(() => {
  planId && getTestInfo();
});

/** 预览文件 **/

function initWps(token, url) {
  const wpsServer = WPS.config({
    mode: 'simple',
    url: url,
    mount: document.getElementById('__pre__'),
  });
  wpsServer.setToken({ token });
  wpsServer.on('error', () => {
    console.log('error');
    hasTest.value = false;
  });
  wpsServer.on('fileOpen', () => {
    console.log('fileOpen');
    if (courseId && hourId) {
      reportFileStatus();
    }
  });
}

// 文件观看上报
function reportFileStatus() {
  try {
    PlayerApi.reportVideoFinish(courseId, hourId, inPlan);
  } catch (e) {
    console.log('文件观看上报失败，请稍后重试', e);
  }
}
</script>

<template>
  <div class="h-full flex flex-col overflow-hidden">
    <van-nav-bar :title="name || '查看文件'" class="my-text-one-line-overflow" left-arrow left-text="返回" @click-left="router.back()"></van-nav-bar>
    <van-watermark :content="userInfo.name + userInfo.mobile" :gap-x="30" :gap-y="50" :opacity="0.5" />
    <div class="flex-1 overflow-hidden" id="__pre__"></div>
    <div v-show="hasTest" class="bg-white flex justify-center py-2">
      <div @click="router.push(`/test/${courseId}/${hourId}/${planId}`)" class="w-[650px] h-[90px] bg-blue rounded-[15px] leading-[90px] text-center">
        <span class="text-[34px] font-font font-normal text-white">随堂考试</span>
      </div>
    </div>
  </div>
</template>
