<script setup>
import { useRoute, useRouter } from 'vue-router';
import { onBeforeUnmount, onMounted, ref } from 'vue';
import { PlayerApi, TestApi } from '@/api';
import { getPureImageUrl } from '@/utils/image';
const router = useRouter();
const route = useRoute();
/**
 * 水印
 */

const userInfo = sessionStorage.getItem('__user__') && JSON.parse(sessionStorage.getItem('__user__'));

/** 页面初始化 **/

let url = sessionStorage.getItem('__preview_image_url__');

if (!url) {
  router.back();
} else {
  url = getPureImageUrl(url);
}

const { name, courseId, hourId, planId } = route.params;
const { inPlan } = route.query;
const ext = name.substring(name.lastIndexOf('.') + 1);

onBeforeUnmount(() => {
  sessionStorage.removeItem('__preview_image_url__');
});

/**
 * 获取考试信息
 */

const hasTest = ref(false);

const getTestInfo = async () => {
  try {
    const res = await TestApi.getCourseHourQuestion(courseId, hourId, planId);
    if (res.code !== 0) {
      throw new Error(res.msg);
    }
    sessionStorage.setItem('__test_info__', JSON.stringify(res.data));
    hasTest.value = true;
  } catch (error) {
    console.log(error.message);
  }
};

onMounted(() => {
  planId && getTestInfo();
  reportFileStatus();
});

// 文件观看上报
function reportFileStatus() {
  if (!courseId || !hourId) return;
  try {
    PlayerApi.reportVideoFinish(courseId, hourId, inPlan);
  } catch (e) {
    console.log('图片上报失败，请稍后重试', e);
  }
}
</script>

<template>
  <div class="h-full overflow-hidden flex flex-col">
    <!-- <van-nav-bar :title="name || '查看图片'" class="my-text-one-line-overflow" left-arrow left-text="返回" @click-left="router.back()"></van-nav-bar> -->
    <van-watermark :content="userInfo.name + userInfo.mobile" :gap-x="30" :gap-y="50" :opacity="0.5" />
    <div id="test" class="flex-1 bg-black flex items-center overflow-auto">
      <van-image-preview :show="true" :images="[url]" :show-index="false" />
    </div>
    <div v-if="hasTest" class="bg-white flex justify-center py-2">
      <div @click="router.push(`/test/${courseId}/${hourId}/${planId}`)" class="w-[650px] h-[90px] bg-blue rounded-[15px] leading-[90px] text-center">
        <span class="text-[34px] font-font font-normal text-white">随堂考试</span>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
