<script setup>
import Player from 'xgplayer';
import { useRoute, useRouter } from 'vue-router';
import { computed, onBeforeUnmount, onMounted, ref } from 'vue';
import WaterMark from '@/views/Player/WaterMark.vue';

const router = useRouter();
const route = useRoute();

/**
 * 水印
 */

const showWatermark = ref(false);

const userInfo = sessionStorage.getItem('__user__') && JSON.parse(sessionStorage.getItem('__user__'));

/** 获取 bar-height */

let barHeight = (sessionStorage.getItem('__barHeight__') || 40) + 'px';

const marginX = computed(() => (isFullScreen.value ? barHeight : '0px'));

/** 初始化播放器 */

const url = sessionStorage.getItem('__player_url__');

if (!url) {
  router.back();
}

let player = null;
const isFullScreen = ref(false);
const controlShow = ref(false);

onMounted(() => {
  player = new Player({
    id: '__simple_player__',
    url,
    rotate: {   //视频旋转按钮配置项
      innerRotate: true, //只旋转内部video
      clockwise: false // 旋转方向是否为顺时针
    },
    fluid: true,
    autoplay: true,
    playbackRate: [0.75, 1, 1.25, 1.5, 2],
    defaultPlaybackRate: 1,
    playsinline: true,
    rotateFullscreen: true,
    cssFullscreen: false,
    closeVideoClick: true,
    lastPlayTimeHideDelay: 3,
  });

  player.on('ready', () => {
    player.getRotateFullscreen();
  });

  player.on('play', () => {
    !showWatermark.value && (showWatermark.value = true);
  });

  player.on('getRotateFullscreen', () => {
    isFullScreen.value = true;
  });

  player.on('exitRotateFullscreen', () => {
    isFullScreen.value = false;
  });

  player.on('controlShow', () => {
    controlShow.value = true;
  });

  player.on('controlHide', () => {
    controlShow.value = false;
  });
});

onBeforeUnmount(() => {
  player.destroy(true);
  sessionStorage.removeItem('__player_url__');
});
</script>

<template>
  <div class="h-full flex items-center bg-black">
    <water-mark v-if="showWatermark" :content="userInfo.name + userInfo.mobile" :is-full-screen="isFullScreen" />
    <div id="__simple_player__">
      <div
        @click="router.back()"
        v-show="isFullScreen && controlShow"
        class="absolute left-[70px] right-0 flex items-center text-white z-[200] bg-black"
        style="font-size: 29px; line-height: 1.5"
      >
        <img class="w-[50px]" src="@/assets/detail/back.png" alt="" />
        返回
      </div>
      <div
        @click="router.back()"
        v-show="!isFullScreen && controlShow"
        class="absolute left-0 right-0 top-0 flex items-center text-white z-[200] bg-black"
        style="font-size: 20px; line-height: 1.5"
      >
        <img class="w-[40px]" src="@/assets/detail/back.png" alt="" />
        返回
      </div>
    </div>
  </div>
</template>

<style scoped>
:deep(.xgplayer-controls) {
  margin: 0 v-bind(marginX);
}
</style>
