<template>
  <NavBar :title="'上课提醒'">
    <!-- <img style="width: 18px" src="@/assets/remind/search.png" alt="" /> -->
  </NavBar>
  <div class="remind">
    <div class="r-item" v-for="item in MyCourse" :key="item.id">
      <Curriculum :course-data="item"></Curriculum>
    </div>
  </div>
  <Loading :isShow="loadingShow"></Loading>
</template>

<script setup>
import { ref } from 'vue';

import Curriculum from '@/components/Curriculum.vue';

import { getMyCourse, getStudyTime } from '@/api/course.js';
import { getCourseState } from '@/utils/index.js';

const MyCourse = ref([]);
let loadingShow = ref(true);
const initData = () => {
  // 获取我的课程
  getMyCourse().then((res) => {
    loadingShow.value = false;
    res.forEach((item) => {
      let courseState = getCourseState(item.startTime, item.endTime, item.type, item.status);
      if (courseState === '已开课') {
        MyCourse.value.push(item);
      }
    });
  });
};
initData();
</script>

<style lang="scss" scoped>
.remind {
  overflow: auto;
  padding: 30px;
  padding-top: 1px;
  height: calc(100vh - 180px);
  background-color: rgb(245, 246, 247);
  .r-item {
    margin: 16px 0;
  }
}
</style>
