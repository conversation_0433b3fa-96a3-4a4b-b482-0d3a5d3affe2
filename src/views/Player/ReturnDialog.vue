<script setup>
const props = defineProps({
  isFullScreen: {
    type: Boolean,
    default: true,
  },
  isFirst: {
    type: Boolean,
    default: true,
  },
});
const emit = defineEmits(['confirmReturnDialog']);
</script>

<template>
  <div class="fixed left-0 top-0 right-0 bottom-0 flex justify-center items-center z-[120]">
    <div class="flex flex-col items-center px-[30px] pt-[30px] w-[500px] rounded-2xl bg-white" :class="{ 'rotate-90': props.isFullScreen }">
      <div class="px-[20px] text-center">
        <span class="text-[30px] font-font font-normal text-simple"
          >您未在规定时间内点击确认，视频将跳转至{{ props.isFirst ? '开头部分' : '上一次的确认进程' }}，请认真观看</span
        >
      </div>
      <div class="border-t my-[16px] w-[100%] text-center" @click="emit('confirmReturnDialog')">
        <span class="text-[30px] font-font font-normal text-blue leading-[70px]">确认</span>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
