<script setup>
const props = defineProps({
  isFullScreen: {
    type: Boolean,
    default: true,
  },
});
const emit = defineEmits(['confirmTestDialog']);
</script>

<template>
  <div class="fixed left-0 top-0 right-0 bottom-0 flex justify-center items-center z-[120]">
    <div class="flex flex-col items-center px-[34px] rounded-2xl bg-white" :class="{ 'rotate-90': props.isFullScreen }">
      <div class="mt-[50px] mb-[48px] w-[350px] text-[30px] font-font font-normal text-simple text-center">
        <p>课程播放完毕，</p>
        <p>请继续完成随堂考试内容</p>
      </div>
      <div class="w-[433px] h-[80px] leading-[80px] border-t border-[#DBDDDF] text-center" @click="emit('confirmTestDialog')">
        <span class="text-[30px] font-font font-normal text-blue">确认</span>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
