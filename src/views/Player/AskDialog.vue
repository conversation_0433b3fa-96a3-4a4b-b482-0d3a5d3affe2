<script setup>
import { ref, onUnmounted, onMounted } from 'vue';

const props = defineProps({
  isFullScreen: {
    type: Boolean,
    default: true,
  },
});
const emit = defineEmits(['confirmAckDialog', 'timeoutAckDialog']);

let timeId = null;
const time = ref(60);

onMounted(() => {
  timeId = setInterval(() => {
    if (time.value === 0) {
      clearInterval(timeId);
      emit('timeoutAckDialog');
      return;
    }
    time.value--;
  }, 1000);
});

onUnmounted(() => {
  clearInterval(timeId);
});
</script>

<template>
  <div class="fixed left-0 top-0 right-0 bottom-0 flex justify-center items-center z-[120]">
    <div class="flex flex-col items-center px-[30px] pt-[30px] rounded-2xl bg-white" :class="{ 'rotate-90': props.isFullScreen }">
      <img class="w-[133px] h-[124px] mb-[20px]" src="@/assets/video/ack.webp" alt="" />
      <span class="text-[30px] font-font font-normal text-simple">你还在看吗</span>
      <div class="border-t border-[#DBDDDF] my-[16px] w-[265px] text-center" @click="emit('confirmAckDialog')">
        <span class="text-[30px] font-font font-normal text-simple leading-[70px]">确认({{ time }})</span>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
