<script setup>
import { onMounted, ref, watch } from 'vue';

const props = defineProps({
  content: String,
  isFullScreen: {
    type: Boolean,
    default: true,
  },
});

const waterMark = ref(null);

const docHeight = document.documentElement.clientHeight;

const docWidth = document.documentElement.clientWidth;

let animate;

function moveWatermark() {
  let keyframes;

  if (props.isFullScreen) {
    waterMark.value.style.left = Math.floor(Math.random() * (60 - 10)) + 10 + '%';
    waterMark.value.style.bottom = '0px';
    keyframes = [{ transform: `translateY(${0}px) rotate(90deg)` }, { transform: `translateY(-${docHeight}px) rotate(90deg)` }];
  } else {
    waterMark.value.style.top = docHeight / 2 + 'px';
    waterMark.value.style.left = -100 + 'px';
    keyframes = [{ transform: `translateX(-${100}px)` }, { transform: `translateX(${docWidth + 100}px)` }];
  }

  animate = waterMark.value.animate(keyframes, {
    duration: 9000,
    easing: 'linear',
    iterations: 1,
    fill: 'forwards',
  });

  animate.onfinish = () => {
    moveWatermark();
  };
}

watch(
  () => props.isFullScreen,
  () => {
    if (waterMark.value) {
      animate && animate.cancel();
      moveWatermark();
    }
  },
);

onMounted(() => {
  moveWatermark();
});
</script>

<template>
  <div ref="waterMark" class="absolute w-fit h-fit z-[100] pointer-events-none text-white [text-shadow:_0_1px_0_rgb(0_0_0_/_40%)] opacity-60">
    {{ content }}
  </div>
</template>

<style scoped lang="scss"></style>
