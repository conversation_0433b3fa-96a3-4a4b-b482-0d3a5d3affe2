<script setup>
import Player from 'xgplayer';
import { useRoute, useRouter } from 'vue-router';
import { computed, onBeforeUnmount, onMounted, ref } from 'vue';
import { PlayerApi, TestApi } from '@/api';
import AskDialog from './AskDialog.vue';
import ReturnDialog from './ReturnDialog.vue';
import TestDialog from '@/views/Player/TestDialog.vue';
import Bus from '@/utils/bus';
import WaterMark from '@/views/Player/WaterMark.vue';

const router = useRouter();
const route = useRoute();
const { planId, courseId, hourId } = route.params;
const { inPlan } = route.query;

/**
 * 水印
 */

const showWatermark = ref(false);

const userInfo = sessionStorage.getItem('__user__') && JSON.parse(sessionStorage.getItem('__user__'));

/** 获取 bar-height */

let barHeight = (sessionStorage.getItem('__barHeight__') || 40) + 'px';

const marginX = computed(() => (isFullScreen.value ? barHeight : '0px'));

/** 初始化页面 */

const url = sessionStorage.getItem('__player_url__');

if (!url) {
  router.back();
}

onMounted(() => {
  getVideoDetail();
  planId && getTestInfo();
  // 锁屏检测
  Bus.on('app_back', () => {
    router.back();
    Bus.off('app_back');
  });
});

onBeforeUnmount(() => {
  reportBeforeLeave();
  player.destroy(true);
  sessionStorage.removeItem('__player_url__');
});

/** 获取课时信息 */

const preDuration = ref(0);
const duration = ref(0);
const isFinished = ref(false);
const lastReportTime = ref(0);

async function getVideoDetail() {
  try {
    const res = await PlayerApi.getCourseHourDetail(courseId, hourId, planId);
    if (res.code === 0) {
      preDuration.value = res.data.user_hour_record ? res.data.user_hour_record.finishedDuration : 0;
      lastReportTime.value = preDuration.value;
      duration.value = res.data.hour.duration;
      isFinished.value = res.data.user_hour_record ? res.data.user_hour_record.isFinished : false;
      initPlayer();
    } else {
      console.log(res.msg || res.message);
      router.back();
    }
  } catch (e) {
    console.log('获取课时信息失败，请稍后重试', e);
    router.back();
  }
}

/** 获取考试信息 */

let hasTest = false;

async function getTestInfo() {
  try {
    const res = await TestApi.getCourseHourQuestion(courseId, hourId, planId);
    if (res.code === 0) {
      sessionStorage.setItem('__test_info__', JSON.stringify(res.data));
      hasTest = true;
    } else {
      console.log(res.msg || res.message);
    }
  } catch (e) {
    console.log('获取考试信息失败，请稍后重试', e);
  }
}

/** 初始化播放器 */

let player = null;
let timer = null;
let localTimer = null;
let timerArr = [];
const isFullScreen = ref(false);
const controlShow = ref(false);
const askPoint = ref(-1);

function initPlayer() {
  player = new Player({
    id: '__player__',
    url,
    rotate: {
      //视频旋转按钮配置项
      innerRotate: true, //只旋转内部video
      clockwise: false, // 旋转方向是否为顺时针
    },
    lastPlayTime: preDuration.value / 1000,
    fluid: true,
    autoplay: true,
    playbackRate: [0.75, 1, 1.25, 1.5, 2],
    defaultPlaybackRate: 1,
    playsinline: true,
    rotateFullscreen: true,
    cssFullscreen: false,
    closeVideoClick: true,
    lastPlayTimeHideDelay: 3,
    allowSeekPlayed: inPlan === 'true',
  });

  player.on('ready', () => {
    player.getRotateFullscreen();
    if (isFinished.value === false) timerArr = getTimeNodes(preDuration.value, duration.value);
    console.log('arr', timerArr);
  });

  player.on('getRotateFullscreen', () => {
    isFullScreen.value = true;
  });

  player.on('exitRotateFullscreen', () => {
    isFullScreen.value = false;
  });

  player.on('controlShow', () => {
    controlShow.value = true;
  });

  player.on('controlHide', () => {
    controlShow.value = false;
  });

  player.on('play', () => {
    !showWatermark.value && (showWatermark.value = true);
    !localTimer &&
      (localTimer = setInterval(() => {
        sessionStorage.setItem('__player_duration__', JSON.stringify(Math.floor(player.currentTime) * 1000));
      }, 1000));
    !timer &&
      (timer = setInterval(() => {
        reportPlay(lastReportTime.value, Math.floor(player.currentTime) * 1000);
      }, 30000));
  });

  if (isFinished.value === false) {
    player.on('timeupdate', () => {
      if (timerArr.length === 0) return;
      const currentIndex = timerArr.findIndex((item) => item === Math.floor(player.currentTime) * 1000);
      if (currentIndex !== -1 && currentIndex > askPoint.value) {
        askPoint.value = currentIndex;
        player.pause();
        showAskDialog.value = true;
      }
    });
  }

  player.on('seeked', () => {
    lastReportTime.value = Math.floor(player.currentTime) * 1000;
  });

  player.on('ended', () => {
    hasTest ? (showTestDialog.value = true) : reportBeforeLeave();
    reportFinish();
  });

  player.on('error', () => {
    router.back();
  });
}

/** 视频数据上报 */

const planIds = JSON.parse(sessionStorage.getItem('__course_planId__')) || [''];

async function reportPlay(startTime, endTime) {
  try {
    await PlayerApi.reportVideoStatus(courseId, hourId, inPlan, {
      startPoint: startTime,
      endPoint: endTime,
      planIds,
    });
    await PlayerApi.reportVideoProgress(courseId, hourId, inPlan, {
      startPoint: startTime,
      endPoint: endTime,
      planIds,
    });
    lastReportTime.value = endTime;
  } catch (e) {
    console.log('上报视频播放状态失败', e);
  }
}

// 退出上报
async function reportBeforeLeave() {
  if (timer) {
    clearInterval(timer);
    timer = null;
  }
  if (localTimer) {
    clearInterval(localTimer);
    localTimer = null;
  }
  const startTime = lastReportTime.value;
  const endTime = JSON.parse(sessionStorage.getItem('__player_duration__'));
  await reportPlay(startTime, endTime);
  lastReportTime.value = 0;
  sessionStorage.setItem('__player_duration__', String(0));
}

// 上报视频观看完成
function reportFinish() {
  PlayerApi.reportVideoFinish(courseId, hourId, inPlan)
    .then((res) => {
      if (res.code === 0) {
        console.log('上报视频观看完成成功');
      } else {
        console.log(res.msg || res.message);
      }
    })
    .catch((e) => {
      console.log('上报视频观看完成失败', e);
    });
}

/**
 * 通知返回上一个观看节点
 */

const showReturnDialog = ref(false);

function handleReturnLastPoint() {
  askPoint.value--;
  player.currentTime = (timerArr[askPoint.value] || 0) / 1000;
  player.play();
  showReturnDialog.value = false;
}

/** 询问是否继续学习 */

const showAskDialog = ref(false);

// 继续播放
function handleContinue() {
  showAskDialog.value = false;
  player.play();
}

// 超时退出
function handleTimeout() {
  showAskDialog.value = false;
  showReturnDialog.value = true;
}

// 根据时长计算暂停时间节点
function getTimeNodes(preDuration, duration) {
  const oneHour = 60 * 60 * 1000; // 一小时的毫秒数
  const fiveMinutes = 5 * 60 * 1000; // 五分钟的毫秒数
  const tenMinutes = 10 * 60 * 1000; // 十分钟的毫秒数

  // 计算起始时间和结束时间之间的时间间隔
  const timeDiff = duration - preDuration;

  // 如果时间间隔大于等于一小时，则按十分钟为单位获取时间节点
  if (timeDiff >= oneHour) {
    const timeNodes = [];
    let currentTime = preDuration;

    while (currentTime <= duration) {
      timeNodes.push(currentTime);
      currentTime += tenMinutes;
    }

    timeNodes.shift();

    return timeNodes;
  }
  // 如果时间间隔大于等于五分钟，则按五分钟为单位获取时间节点
  else if (timeDiff >= fiveMinutes) {
    const timeNodes = [];
    let currentTime = preDuration;

    while (currentTime <= duration) {
      timeNodes.push(currentTime);
      currentTime += fiveMinutes;
    }

    timeNodes.shift();

    return timeNodes;
  }
  // 如果时间间隔小于五分钟，则返回空数组
  else {
    return [];
  }
}

/** 课程测试 */

const showTestDialog = ref(false);

function handleGoToTest() {
  showTestDialog.value = false;
  router.back();
  setTimeout(() => {
    router.push(`/test/${courseId}/${hourId}/${planId}`);
  }, 100);
}
</script>

<template>
  <div class="h-full w-full flex items-center justify-center bg-black overflow-hidden">
    <ask-dialog
      v-if="showAskDialog"
      :is-full-screen="isFullScreen"
      @confirm-ack-dialog="handleContinue"
      @timeout-ack-dialog="handleTimeout"
    ></ask-dialog>
    <return-dialog
      v-if="showReturnDialog"
      :is-full-screen="isFullScreen"
      :is-first="askPoint === -1"
      @confirm-return-dialog="handleReturnLastPoint"
    />
    <test-dialog v-if="showTestDialog" :is-full-screen="isFullScreen" @confirm-test-dialog="handleGoToTest"></test-dialog>

    <water-mark v-if="showWatermark" :content="userInfo.name + userInfo.mobile" :is-full-screen="isFullScreen" />

    <div id="__player__">
      <div
        @click="router.back()"
        v-show="isFullScreen && controlShow"
        class="absolute left-[70px] right-0 flex items-center text-white z-[200] bg-black"
        style="font-size: 29px; line-height: 1.5"
      >
        <img class="w-[50px]" src="@/assets/detail/back.png" alt="" />
        返回
      </div>
      <div
        @click="router.back()"
        v-show="!isFullScreen && controlShow"
        class="absolute left-0 right-0 top-0 flex items-center text-white z-[200] bg-black"
        style="font-size: 20px; line-height: 1.5"
      >
        <img class="w-[40px]" src="@/assets/detail/back.png" alt="" />
        返回
      </div>
    </div>
  </div>
</template>

<style scoped>
:deep(.xgplayer-controls) {
  margin: 0 v-bind(marginX);
}
</style>
