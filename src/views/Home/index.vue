<script setup>
import { computed, reactive, ref, watch } from 'vue';
import { HomeApi } from '@/api';
import { useRouter } from 'vue-router';
import { backClient } from '@/utils';
import { useSessionStorage } from '@vueuse/core';
import Banner1 from '@/assets/home/<USER>/banner-1.png';
import Banner2 from '@/assets/home/<USER>/banner-2.png';
import StarCourseIcon from '@/assets/home/<USER>';
import AllCourseIcon from '@/assets/home/<USER>';
import EnterPoint from '@/components/EnterPoint.vue';
import FilterIcon from '@/assets/base/filter.png';
import CourseCard from '@/components/CourseCard.vue';
import UserCountIcon from '@/assets/home/<USER>';

const router = useRouter();
const showCategory = ref(false);
const sortType = ref(0);

function handleSortTypeChange(value) {
  sortType.value = value;
  getAllCourseList();
}

/**
 * 滚动
 */

const scrollRef = ref(null);

function handleScrollToTop() {
  scrollRef.value?.scrollIntoView({
    behavior: 'smooth',
    block: 'start',
  });
}

/**
 * 分类
 */

const themeVars = {
  dropdownMenuHeight: '40px',
  dropdownMenuShadow: 'none',
  dropdownMenuTitleFontSize: '14px',
  sidebarLineHeight: '15px',
  sidebarWidth: '90px',
  sidebarSelectedBorderColor: '#326FFF',
  sidebarSelectedBackground: '#F5F6F7',
};

const allCategory = useSessionStorage('train_mobile_all_category', null, {
  serializer: {
    read: (v) => (v ? JSON.parse(v) : null),
    write: (v) => JSON.stringify(v),
  },
});

const categoryList = reactive([]);

const checkCategoryChain = useSessionStorage('train_mobile_check_category', []);

const curCategoryId = computed(() => {
  if (checkCategoryChain.value.length === 0) return ''; // 避免slice对空数组操作
  const id = checkCategoryChain.value.slice(-1)[0] || '';
  const ids = id.split('-');
  return ids[ids.length - 1];
});

const ratingCourseMap = useSessionStorage('train_mobile_rating_course', {});

function initFirmCategoryList(changeFirmId) {
  if (changeFirmId) {
    checkCategoryChain.value = [changeFirmId];
    categoryList.splice(1); // 清除公司之后的所有层级数据
  }

  const cacheFirmId = checkCategoryChain.value[0];
  categoryList[0] = allCategory.value?.['0'] || [];
  // 确保 cacheFirmId 有效或选择第一个作为默认
  const firmExists = categoryList[0].some((f) => f.id === cacheFirmId);
  checkCategoryChain.value[0] = firmExists && cacheFirmId ? cacheFirmId : categoryList[0][0]?.id || '';

  // 如果没有公司被选中（例如 allCategory['0'] 为空或 cacheFirmId 无效），则不继续
  if (!checkCategoryChain.value[0]) {
    categoryList.splice(1); // 确保后续层级清空
    return;
  }

  // 计算部门 tab
  const cacheDeptId = checkCategoryChain.value[1]; // 可能为 undefined
  const firmId = checkCategoryChain.value[0];
  const firm = categoryList[0].find((cate) => cate.id === firmId);

  if (!firm) {
    // 如果找不到公司数据，清空后续层级
    categoryList.splice(1);
    return;
  }

  const topCategory = {
    ...firm,
    name: '全部部门',
  };
  categoryList[1] = [topCategory, ...(allCategory.value?.[firmId] || [])];
  // 确保 cacheDeptId 有效或选择 firmId ("全部部门") 作为默认
  const deptExists = categoryList[1].some((d) => d.id === cacheDeptId);
  checkCategoryChain.value[1] = deptExists && cacheDeptId ? cacheDeptId : firmId;

  initDeptCategoryList();
}

function initDeptCategoryList(changeDeptId) {
  if (changeDeptId) {
    // 必须保证公司层已被选择
    if (!checkCategoryChain.value[0]) return;
    checkCategoryChain.value = [checkCategoryChain.value[0], changeDeptId];
    categoryList.splice(2); // 清除部门之后的所有层级数据
  }

  const firmIdFromChain = checkCategoryChain.value[0]; // 当前选中的公司ID
  const deptId = checkCategoryChain.value[1]; // 当前选中的部门ID
  const dept = categoryList[1]?.find((cate) => cate.id === deptId);

  if (!dept) {
    // 如果找不到部门数据，清空后续层级
    categoryList.splice(2);
    return;
  }

  const topCategory = {
    ...dept,
    name: '全部课程',
  };

  let list = [];

  // 只有当选中的不是"全部部门"时，才加载具体部门下的分类和定级培训
  if (deptId !== firmIdFromChain) {
    list = allCategory.value?.[deptId] || [];
    if (list.length > 0) {
      list.forEach((cate) => {
        const children = allCategory.value?.[cate.id];
        cate.children = [{ ...cate, name: '全部' }]; // "全部"子项的ID应为父项ID，方便判断
        if (children && children.length > 0) cate.children.push(...children);
      });
    }
    getRatingTrain(deptId);
  }

  categoryList[2] = [topCategory, ...list];

  const cateId = checkCategoryChain.value[2]; // 可能为 undefined
  // 确保 cateId 有效或选择 deptId ("全部课程") 作为默认
  // 检查 cateId (或其父ID，如果是 'parent-child' 格式) 是否存在于 categoryList[2]
  let currentSelectionInCate = deptId; // 默认选 "全部课程"
  if (cateId) {
    const [mainIdInCate] = cateId.split('-');
    const isValidCate = categoryList[2].some((c) => {
      if (c.id === cateId) return true; // 直接匹配
      if (c.id === mainIdInCate && c.children?.some((child) => child.id === cateId.split('-')[1])) return true;
      return false;
    });
    if (isValidCate) {
      currentSelectionInCate = cateId;
    } else {
      // 如果存储的 cateId 无效，尝试仅用其 mainId (通常是父分类的"全部")
      const mainIdExists = categoryList[2].some((c) => c.id === mainIdInCate);
      if (mainIdExists) currentSelectionInCate = mainIdInCate + '-' + mainIdInCate; // 尝试选中父分类的"全部"
    }
  }
  checkCategoryChain.value[2] = currentSelectionInCate;

  initCateCategoryList();
}

function initCateCategoryList(categoryId) {
  if (categoryId) {
    // 必须保证公司和部门层已被选择
    if (!checkCategoryChain.value[0] || !checkCategoryChain.value[1]) return;
    checkCategoryChain.value = [checkCategoryChain.value[0], checkCategoryChain.value[1], categoryId];
    categoryList.splice(3); // 清除此分类之后的所有层级数据
  }

  const [, deptId, cateIdFromChain, deepIdFromChain] = checkCategoryChain.value;

  // 如果没有选择具体的课程分类 (cateIdFromChain 为空或等于 deptId，即"全部课程")
  if (!cateIdFromChain || deptId === cateIdFromChain) {
    categoryList.splice(3); // 确保第四层及以后为空
    return;
  }

  const [cId, innerId] = cateIdFromChain.split('-'); // cId是父分类ID, innerId是子分类ID (如果存在)
  const actualParentId = innerId ? cId : null; // 如果innerId存在，则cId是父分类的ID
  const actualSelectedId = innerId || cId; // 实际选中的ID (叶子节点或父节点的ID)

  if (cId === 'rating') {
    // 如果是定级培训的子项，它没有更深层级
    console.log(123);

    categoryList.splice(3);
    return;
  }

  // 如果选中了某个课程分类下的"全部" (e.g., cateIdFromChain 是 "groupId-groupId")
  if (actualParentId && actualSelectedId === actualParentId) {
    // 这意味着选中了父分类的 "全部"
    // 下一级的数据应该是 allCategory.value[actualSelectedId]
  } else if (
    !actualParentId &&
    categoryList[2].find((c) => c.id === actualSelectedId && c.children && c.children.length > 1 && c.children[0].id === actualSelectedId)
  ) {
    // 选中了父分类本身，且它有子分类 (children[0]是它的"全部")
    // 下一级的数据应该是 allCategory.value[actualSelectedId]
  } else {
    // 选中了叶子节点，或没有子分类的节点
    const listForNextLevel = allCategory.value?.[actualSelectedId] || [];
    if (listForNextLevel.length === 0) {
      categoryList.splice(3); // 没有更深数据，确保清空
      return;
    }
  }

  const list = allCategory.value?.[actualSelectedId] || [];
  if (list.length === 0) {
    categoryList.splice(3); // 没有更深数据，确保清空
    return;
  }

  // 找到当前选中的分类对象，用于创建下一级的"全部"项
  let cateObj;
  const parentCateInList2 = categoryList[2]?.find((c) => c.id === (actualParentId || actualSelectedId));
  if (actualParentId && parentCateInList2?.children) {
    cateObj = parentCateInList2.children.find((child) => child.id === actualSelectedId);
  } else {
    cateObj = parentCateInList2;
  }
  if (!cateObj) {
    // 如果在 categoryList[2] 中找不到，尝试从 allCategory 获取（作为兜底）
    cateObj = { id: actualSelectedId, name: '未知分类' }; // 基本结构
    const foundInAll = Object.values(allCategory.value || {})
      .flat()
      .find((item) => item.id === actualSelectedId);
    if (foundInAll) cateObj.name = foundInAll.name;
  }

  const topCategory = {
    ...cateObj, // 使用找到的分类对象作为基础
    name: '全部', // 文字是 "全部"
    // id: actualSelectedId, // "全部"项的ID应该是其父ID，即当前选中的ID
  };
  topCategory.id = actualSelectedId;

  categoryList[3] = [topCategory, ...list];
  // 确保 deepIdFromChain 有效或选择 actualSelectedId (下一级的"全部") 作为默认
  let currentSelectionInDeep = actualSelectedId;
  if (deepIdFromChain) {
    const deepIdExists = categoryList[3].some((d) => d.id === deepIdFromChain);
    if (deepIdExists) currentSelectionInDeep = deepIdFromChain;
  }
  checkCategoryChain.value[3] = currentSelectionInDeep;

  initDeepCategoryList(3); // 默认从 categoryList[3] (深层级0) 开始
}

function initDeepCategoryList(deep, categoryId) {
  // deep 是 categoryList 的目标索引 (从3开始)
  if (categoryId) {
    // 更新 checkCategoryChain 到当前深度
    // checkCategoryChain.value.splice(deep, checkCategoryChain.value.length - deep, categoryId);
    checkCategoryChain.value[deep] = categoryId; // 设置当前深度的选择
    categoryList.splice(deep + 1); // 清除此层级之后的所有分类数据
  }

  // 如果 checkCategoryChain 的长度不足以支撑到 deep，或者没有选中项，则停止
  if (checkCategoryChain.value.length <= deep || !checkCategoryChain.value[deep]) {
    categoryList.splice(deep); // 清空当前及后续所有深层分类
    return;
  }

  const currentLevelSelectedIdInChain = checkCategoryChain.value[deep]; // 当前深层级选中的ID (可能是 parent-child)
  const parentLevelSelectedIdInChain = checkCategoryChain.value[deep - 1]; // 上一层选中的ID

  const currentActualId = currentLevelSelectedIdInChain.split('-').pop(); // 取ID的最后一部分
  const parentActualId = parentLevelSelectedIdInChain.split('-').pop();

  if (
    (currentActualId === parentActualId && !currentLevelSelectedIdInChain.includes('-')) ||
    (currentLevelSelectedIdInChain.includes('-') && currentActualId === parentActualId)
  ) {
    if (!parentLevelSelectedIdInChain.startsWith('rating')) {
      categoryList.splice(deep + 1); // 清除下一层级，因为选中了"全部"
      return;
    }
  }

  const list = allCategory.value?.[currentActualId] || [];

  if (list.length === 0) {
    // categoryList.splice(deep + 1); // 确保已清 (通常在 categoryId 分支已处理)
    return;
  }

  // 找到当前选中的 category 对象，用于创建下一级的 "全部"
  let cate;
  // 当前选中的项在 categoryList[deep] 中
  const currentLevelList = categoryList[deep];
  if (currentLevelList) {
    cate = currentLevelList.find((c) => c.id === currentLevelSelectedIdInChain || c.id === currentActualId);
  }
  if (!cate) {
    cate = { id: currentActualId, name: '未知分类' };
    const foundInAll = Object.values(allCategory.value || {})
      .flat()
      .find((item) => item.id === currentActualId);
    if (foundInAll) cate.name = foundInAll.name;
  }

  const topCategory = {
    ...cate,
    name: '全部',
    id: currentActualId,
  };

  categoryList[deep + 1] = [topCategory, ...list];

  const nextLevelStoredSelection = checkCategoryChain.value[deep + 1];
  let validNextLevelSelection = currentActualId; // 默认选"全部"
  if (nextLevelStoredSelection) {
    const nextIdExists = categoryList[deep + 1].some((item) => item.id === nextLevelStoredSelection);
    if (nextIdExists) validNextLevelSelection = nextLevelStoredSelection;
    else {
      const [mainNextId] = nextLevelStoredSelection.split('-');
      if (categoryList[deep + 1].some((item) => item.id === mainNextId)) {
        validNextLevelSelection = mainNextId;
      }
    }
  }
  checkCategoryChain.value[deep + 1] = validNextLevelSelection;
  if (checkCategoryChain.value[deep + 1]) {
    initDeepCategoryList(deep + 1);
  }
}

async function getRatingTrain(categotyId) {
  try {
    const res = await HomeApi.getRatingTrain(categotyId);
    if (res.code !== 0) throw new Error(res.message || 'Failed to fetch rating train');
    if (res.data.length === 0) return;
    res.data.forEach((item) => {
      ratingCourseMap.value[item.id] = item.courseVoList;
    });
    if (categoryList[2] && categoryList[2].length > 0) {
      const ratingCategoryForList = {
        id: 'rating', // 父级ID
        name: '定级培训',
        children: res.data.map(({ id, name }) => ({ id: `rating-${id}`, name })), // 子项ID带上前缀，方便区分
      };
      categoryList[2].splice(1, 0, ratingCategoryForList);
    } else {
      console.warn('categoryList[2] not initialized when getRatingTrain tried to insert data.');
    }
  } catch (e) {
    console.error('获取定级培训失败:', e.message);
  }
}

// 获取分类信息
async function getCategoryInfo() {
  try {
    if (!allCategory.value) {
      const [firmRes, categoryRes] = await Promise.all([HomeApi.getCategoryList(), HomeApi.getAllCategoryList()]);
      if (firmRes.code !== 0 || categoryRes.code !== 0) {
        throw new Error(firmRes.msg || categoryRes.msg || 'Failed to fetch categories');
      }
      const firmCategory = { 0: [] };
      const depts = Object.values(firmRes.data);
      for (let i = 0; i < depts.length; i++) {
        if (depts[i].length === 0) continue;
        const { companyId, place } = depts[i][0];
        firmCategory['0'].push({ id: companyId, name: place });
        firmCategory[companyId] = depts[i].map((dept) => ({ id: dept.categoryId, name: dept.center }));
      }
      allCategory.value = { ...(categoryRes.data.categories || {}), ...firmCategory };
    }
    initFirmCategoryList();
  } catch (e) {
    console.error('获取分类失败:', e.message);
  }
}

getCategoryInfo();

/**
 * 课程相关
 */

const page = ref(0);
const total = ref(0);
const loading = ref(false);
const finished = ref(false);
const searchText = ref('');
const allCourseList = reactive([]);
const starCourseList = ref([]);
const isRequesting = ref(false);

async function getAllCourseList(isSearch = false) {
  if (isRequesting.value) return;

  try {
    isRequesting.value = true;
    const currentCatId = curCategoryId.value;

    if (!currentCatId && !searchText.value && !isSearch) {
      allCourseList.splice(0, allCourseList.length);
      total.value = 0;
      loading.value = false;
      finished.value = true;
      isRequesting.value = false;
      return;
    }

    // 判断是否为定级培训课程
    const lastSelectedInChain = checkCategoryChain.value.slice(-1)[0] || '';
    let isRatingCourseQuery = false;
    let ratingIdForApi = '';

    console.log('lastSelectedInChain:', lastSelectedInChain);

    if (lastSelectedInChain.startsWith('rating-')) {
      isRatingCourseQuery = true;
      ratingIdForApi = lastSelectedInChain.split('-')[1];
    }

    if (isRatingCourseQuery && ratingIdForApi) {
      const res = await HomeApi.getCourseListByRatingId(ratingIdForApi, searchText.value);
      if (res.code !== 0) throw new Error(res.msg);
      allCourseList.splice(0, allCourseList.length, ...(res.data || []));
      total.value = res.data?.length || 0;
      finished.value = true;
      page.value = 1;
      loading.value = false;
      isRequesting.value = false;
      return;
    }

    if (isSearch) {
      page.value = 1;
      allCourseList.splice(0, allCourseList.length);
      finished.value = false; // 重置 finished 状态
    } else {
      page.value += 1;
    }
    const res = await HomeApi.getAllCourseList(page.value, 6, false, searchText.value, currentCatId, sortType.value);
    if (res.code !== 0) throw new Error(res.msg);
    allCourseList.push(...(res.data.courseVoList || []));
    total.value = res.data.total || 0;
  } catch (e) {
    console.error('请求课程列表失败:', e.message);
  } finally {
    loading.value = false;
    finished.value = total.value <= allCourseList.length;
    isRequesting.value = false;
  }
}

async function getStarCourseList() {
  try {
    const res = await HomeApi.getAllCourseList(1, 5, false, '', '', 1);
    if (res.code !== 0) throw new Error(res.msg);
    starCourseList.value = res.data.courseVoList || [];
  } catch (e) {
    console.error('请求精选课程失败:', e.message);
  }
}

getStarCourseList();

// 进入课程详情
function handleCardClick(courseId) {
  router.push(`/details/${courseId}?isHome=true`);
}

watch(
  () => curCategoryId.value,
  (newVal, oldVal) => {
    if (newVal !== oldVal) {
      searchText.value = ''; // 清空搜索框
      getAllCourseList(true); // true 表示是新的分类，应从第一页开始
    }
  },
);
</script>

<template>
  <div class="flex flex-col overflow-hidden">
    <van-config-provider :theme-vars="{ navBarBackground: 'transparent' }">
      <van-nav-bar title="培训系统" :border="false" left-arrow left-text="返回" @click-left="backClient" />
    </van-config-provider>

    <!-- 分类 -->
    <van-config-provider :theme-vars="themeVars">
      <div v-for="(cateList, i) in categoryList.slice(3)" :key="i">
        <van-tabs
          color="#326FFF"
          line-width="80px"
          :ellipsis="false"
          :active="checkCategoryChain[i + 3] || ''"
          @change="(id) => initDeepCategoryList(i + 3, id)"
        >
          <van-tab v-for="cate in cateList" :key="cate.id" :title="cate.name" :name="cate.id"></van-tab>
        </van-tabs>
      </div>
    </van-config-provider>

    <van-action-sheet :show="showCategory" title="分类" class="bg-[#F5F6F7]" @cancel="showCategory = false">
      <div class="bg-white pl-1">
        <van-config-provider :theme-vars="themeVars">
          <!-- 公司 -->
          <van-tabs
            color="#326FFF"
            background="#F5F6F7"
            :ellipsis="false"
            :active="checkCategoryChain[0] || ''"
            @click-tab="(cate) => initFirmCategoryList(cate.name)"
          >
            <van-tab v-for="cate in categoryList[0] || []" :key="cate.id" :title="cate.name" :name="cate.id"></van-tab>
          </van-tabs>
          <!-- 面板 -->
          <div class="flex pt-2 h-[900px] overflow-auto bg-[#F5F6F7] text-[28px]">
            <!-- 侧边 -->
            <div class="h-full bg-[#F5F6F7] overflow-auto">
              <div
                v-for="cate in categoryList[1]"
                :key="cate.id"
                class="relative py-[23px] px-1 w-[190px]"
                :class="cate.id === checkCategoryChain[1] && 'bg-white text-blue'"
                @click="initDeptCategoryList(cate.id)"
              >
                <div class="h-fit px-[22px]">
                  <div
                    v-show="cate.id === checkCategoryChain[1]"
                    class="absolute left-[8px] top-1/2 -translate-y-1/2 w-[6px] h-[46px] bg-blue rounded-[3px]"
                  ></div>
                  {{ cate.name }}
                </div>
              </div>
            </div>

            <div class="flex-1 px-[40px] py-[24px] h-full overflow-auto bg-white">
              <div v-for="(cate, index) in categoryList[2]" :key="cate.id">
                <!-- 部门下全部课程 -->
                <template v-if="index === 0">
                  <div
                    @click="initCateCategoryList(cate.id)"
                    class="mb-[32px] py-1 text-center rounded-xl"
                    :class="cate.id === checkCategoryChain[2] ? 'bg-[#326FFF] text-white' : 'bg-[#F5F6F7]'"
                  >
                    {{ cate.name }}
                  </div>
                </template>
                <!--  部门下分类 -->
                <template v-else>
                  <div class="mb-[32px]">
                    <div>
                      {{ cate.name }}
                    </div>
                    <!--  子部门  -->
                    <div class="flex flex-wrap mt-2">
                      <div
                        v-for="childCate in categoryList[2][index].children"
                        :key="childCate.id"
                        @click="initCateCategoryList(cate.id === 'rating' ? childCate.id : cate.id + '-' + childCate.id)"
                        class="w-fit mr-2 mb-2 py-1 px-3 rounded-xl"
                        :class="
                          (cate.id === 'rating' ? childCate.id : cate.id + '-' + childCate.id) === checkCategoryChain[2]
                            ? 'bg-[#326FFF] text-white'
                            : 'bg-[#F5F6F7]'
                        "
                      >
                        {{ childCate.name }}
                      </div>
                    </div>
                  </div>
                </template>
              </div>
            </div>
          </div>
        </van-config-provider>
      </div>
    </van-action-sheet>

    <!-- 首页课程 -->

    <div class="flex-1 overflow-auto p-[32px] pt-0 mt-[16px]">
      <div class="flex items-center px-[24px] h-[80px] rounded-[16px] bg-white">
        <img src="@/assets/home/<USER>" alt="search" class="w-[30px] h-[30px]" />
        <input
          type="text"
          class="flex-1 text-[28px] ml-[16px]"
          @keypress.enter="getAllCourseList(true)"
          placeholder="搜索课程名称、讲师"
          v-model="searchText"
        />
      </div>

      <div class="mt-[24px] rounded-[20px] overflow-hidden">
        <van-swipe class="my-swipe" :autoplay="3000" indicator-color="white" :show-indicators="false">
          <van-swipe-item>
            <img :src="Banner1" alt="banner" />
          </van-swipe-item>
          <van-swipe-item>
            <img :src="Banner2" alt="banner" />
          </van-swipe-item>
        </van-swipe>
      </div>

      <div class="flex mt-[24px]">
        <img @click="router.push('/star-course')" class="flex-1 h-[160px]" :src="StarCourseIcon" alt="star" />
        <img @click="handleScrollToTop" class="flex-1 h-[160px]" :src="AllCourseIcon" alt="all" />
      </div>

      <div class="mt-[24px]">
        <div class="flex justify-between items-center">
          <div class="text-[26px] font-[600] leading-[48px]">精选课程</div>
          <EnterPoint title="查看更多" @click="router.push('/star-course')" />
        </div>
        <div v-if="starCourseList.length > 0" class="flex p-[32px] mt-[16px] rounded-[16px] bg-white overflow-x-auto">
          <course-card
            v-for="course in starCourseList"
            :key="course.id"
            @click="handleCardClick(course.id)"
            class="flex-shrink-0 mr-[32px]"
            :show-thumbnail="true"
            :title="course.title"
            :thumb="course.thumb"
            :category="course.categoryVo?.name"
            :teacher="course.teacher"
          >
            <template #thumb>
              <div class="flex items-center">
                <img class="w-[32px] h-[32px]" :src="UserCountIcon" alt="" />
                <span class="ml-[8px]">{{ course.userCount }}</span>
              </div>
            </template>
          </course-card>
        </div>
        <div v-else class="flex justify-center items-center p-[32px] mt-[16px] rounded-[16px] text-desc">没有更多了</div>
      </div>

      <div class="mt-[24px]" ref="scrollRef">
        <div class="flex justify-between items-center">
          <div class="text-[26px] font-[600] leading-[48px]">全部课程</div>
          <EnterPoint @click="showCategory = true" title="筛选" :icon="FilterIcon" />
        </div>
        <!-- <div class="flex gap-[16px] mt-[16px]">
          <button
            v-for="item in [
              { label: '最多观看', value: 0 },
              { label: '最新发布', value: 1 },
            ]"
            :key="item.value"
            class="px-[32px] py-[4px] text-[24px] rounded-full"
            :class="item.value === sortType ? 'bg-lightBlue text-primary' : 'bg-white text-desc'"
            @click="handleSortTypeChange(item.value)"
          >
            {{ item.label }}
          </button>
        </div> -->
        <div class="mt-[20px]">
          <van-list v-model:loading="loading" :finished="finished" finished-text="没有更多了" @load="getAllCourseList()">
            <div class="grid grid-cols-2 gap-[24px]">
              <div v-for="course in allCourseList" :key="course.id" @click="handleCardClick(course.id)" class="p-[32px] rounded-[16px] bg-white">
                <course-card
                  :show-thumbnail="true"
                  :title="course.title"
                  :thumb="course.thumb"
                  :category="course.categoryVo?.name"
                  :teacher="course.teacher"
                >
                  <template #thumb>
                    <div class="flex items-center">
                      <img class="w-[32px] h-[32px]" :src="UserCountIcon" alt="" />
                      <span class="ml-[8px]">{{ course.userCount }}</span>
                    </div>
                  </template>
                </course-card>
              </div>
            </div>
          </van-list>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
::v-deep .van-dropdown-menu__bar {
  box-shadow: none;
}

::v-deep .van-tabs__wrap {
  border-bottom: 1px solid #e5e5e5;
}
</style>
