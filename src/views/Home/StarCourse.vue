<script setup>
import { ref, reactive } from 'vue';
import CourseCard from '@/components/CourseCard.vue';
import { useRouter } from 'vue-router';
import { HomeApi } from '@/api';

const router = useRouter();

const loading = ref(false);
const finished = ref(false);
const page = ref(0);
const searchText = ref('');
const starCourseList = reactive([]);
const total = ref(0);
const isRequesting = ref(false);

async function getAllCourseList(isSearch = false) {
  if (isRequesting.value) return;

  try {
    isRequesting.value = true;

    if (isSearch) {
      page.value = 1;
      starCourseList.splice(0, starCourseList.length);
    } else {
      page.value += 1;
    }

    const res = await HomeApi.getAllCourseList(page.value, 6, false, searchText.value, '', 1);
    if (res.code !== 0) throw new Error(res.msg);
    starCourseList.push(...res.data.courseVoList);
    total.value = res.data.total;
  } catch (e) {
    console.log('请求精选课程失败， 请稍后重试', e);
  } finally {
    loading.value = false;
    finished.value = total.value <= starCourseList.length;
    isRequesting.value = false;
  }
}

// 进入课程详情
function handleCardClick(courseId) {
  router.push(`/details/${courseId}?isHome=true`);
}
</script>

<template>
  <div class="flex flex-col overflow-hidden bg-white">
    <van-nav-bar title="精选课程" :border="false" left-arrow left-text="返回" @click-left="router.back()" />

    <div class="flex items-center mx-[32px] px-[24px] h-[80px] rounded-[16px] bg-[#F5F6F7]">
      <img src="@/assets/home/<USER>" alt="search" class="w-[30px] h-[30px]" />
      <input
        type="text"
        class="flex-1 text-[28px] ml-[16px] bg-transparent"
        @keypress.enter="getAllCourseList(true)"
        placeholder="搜索课程名称、讲师"
        v-model="searchText"
      />
    </div>

    <div class="flex-1 flex flex-col mt-[32px] px-[32px] overflow-auto">
      <van-list v-model:loading="loading" :finished="finished" finished-text="没有更多了" @load="getAllCourseList()">
        <div v-for="course in starCourseList" :key="course.id" @click="handleCardClick(course.id)" class="mb-[16px]">
          <course-card mode="horizontal" :title="course.title" :thumb="course.thumb" :category="course.categoryVo?.name" :teacher="course.teacher">
            <template #default>
              <span class="text-[24px] text-desc">{{ course.userCount }}人参与</span>
            </template>
          </course-card>
        </div>
      </van-list>
    </div>
  </div>
</template>
