<script setup>
import { ref, onMounted, computed } from 'vue';
import CompanyIcon from '@/assets/bank/company-icon.png';
import CompanyActiveIcon from '@/assets/bank/company-active.png';
import { HomeApi } from '@/api';
import { showToast } from 'vant';

const CATEGORY_CACHE_KEY = 'select_category';

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true,
  },
});

const emit = defineEmits(['update:modelValue', 'submit']);

// =============== 数据 ===============

const categoryInfo = ref(null);
const activeChoose = ref({
  firm: null,
  categoryId: null,
  center: null,
});

const companyList = computed(() => Object.keys(categoryInfo.value));
const departmentList = computed(() => categoryInfo.value[activeChoose.value.firm]);

const handleChooseCompany = (firm) => {
  const firstDept = categoryInfo.value[firm]?.[0];
  if (!firstDept) {
    activeChoose.value = { firm, categoryId: null, center: null };
  } else {
    activeChoose.value = { firm, categoryId: firstDept.categoryId, center: firstDept.center };
  }
};

const handleChooseDepartment = (department) => {
  activeChoose.value.categoryId = department.categoryId;
  activeChoose.value.center = department.center;
};

// =============== 弹窗 ===============

const isCompany = ref(true);

const nextStep = () => {
  isCompany.value = false;
};

const handleModalClose = () => {
  const selectCategory = localStorage.getItem(CATEGORY_CACHE_KEY) || sessionStorage.getItem(CATEGORY_CACHE_KEY);
  if (selectCategory) {
    activeChoose.value = JSON.parse(selectCategory);
  }
  emit('update:modelValue', false);
};

const handleModalConfirm = () => {
  localStorage.setItem(CATEGORY_CACHE_KEY, JSON.stringify(activeChoose.value));
  sessionStorage.setItem(CATEGORY_CACHE_KEY, JSON.stringify(activeChoose.value));
  emit('update:modelValue', false);
  emit('submit', activeChoose.value);
};

// =============== 检查缓存 ===============

const showChooseCompanyModal = ref(false);

const getCategoryInfo = async () => {
  try {
    const res = await HomeApi.getCategoryList();
    if (res.code !== 0) throw new Error(res.msg || '获取分类失败');
    categoryInfo.value = res.data;
  } catch (e) {
    showToast(e.message);
  }
};

const checkCategory = async () => {
  try {
    await getCategoryInfo();
    if (!categoryInfo.value) return;
    const selectCategory = localStorage.getItem(CATEGORY_CACHE_KEY) || sessionStorage.getItem(CATEGORY_CACHE_KEY);
    if (!selectCategory) {
      showChooseCompanyModal.value = true;
    } else {
      const { center, categoryId, firm } = JSON.parse(selectCategory);
      if (!categoryInfo.value[firm] || !categoryInfo.value[firm].find((item) => item.categoryId === categoryId)) {
        // 公司或部门不存在，则重新选择
        showChooseCompanyModal.value = true;
      } else {
        activeChoose.value = { firm, categoryId, center };
        emit('submit', activeChoose.value);
      }
    }
  } catch (error) {
    showToast(error.message);
  }
};

onMounted(() => {
  checkCategory();
});
</script>

<template>
  <van-popup
    :show="modelValue"
    position="bottom"
    round
    :style="{ height: '90%' }"
    :close-on-click-overlay="false"
  >
    <div class="h-full">
      <div class="flex justify-between items-center px-[32px] h-[112px] border-b border-solid border-[#E5E5E5]">
        <button class="text-[28px] leading-[44px] text-[#2D6DFF] border-none bg-transparent" @click="handleModalClose">取消</button>
        <button class="text-[28px] leading-[44px] text-[#2D6DFF] border-none bg-transparent" @click="handleModalConfirm">确定</button>
      </div>
      <p class="mt-[16px] px-[32px] py-[8px] text-[28px] font-medium leading-[44px]">公司名称</p>
      <div class="mt-[8px] grid grid-cols-2 gap-[16px] px-[24px]">
        <button
          v-for="firm in companyList"
          :key="firm"
          class="py-[16px] rounded-[12px] text-[24px] leading-[36px]"
          :class="[activeChoose.firm === firm ? 'bg-[#2C67EC] text-white' : 'bg-[#F5F7FA] text-[#1D2129]/60']"
          @click="handleChooseCompany(firm)"
        >
          {{ firm }}
        </button>
      </div>
      <p class="mt-[24px] px-[32px] py-[8px] text-[28px] font-medium leading-[44px]">部门名称</p>
      <div class="mt-[8px] grid grid-cols-4 gap-[16px] px-[24px]">
        <button
          v-for="department in departmentList"
          :key="department.categoryId"
          class="py-[16px] rounded-[12px] text-[24px] leading-[36px]"
          :class="[activeChoose.categoryId === department.categoryId ? 'bg-[#2C67EC] text-white' : 'bg-[#F5F7FA] text-[#1D2129]/60']"
          @click="handleChooseDepartment(department)"
        >
          {{ department.center }}
        </button>
      </div>
    </div>
  </van-popup>

  <!-- 弹窗 -->
  <Teleport to="body">
    <Transition name="modal-fade">
      <div class="modal-overlay" v-if="showChooseCompanyModal">
        <Transition name="modal-slide">
          <div class="modal-content" v-if="showChooseCompanyModal" @click.stop>
            <img :src="CompanyIcon" alt="company-icon" class="absolute right-[20px] top-[-60px] w-[260px] h-[286px]" />
            <p class="text-[40px] font-medium text-white">{{ isCompany ? '请选择所在公司' : '请选择所在部门' }}</p>
            <div class="flex-1 grid gap-[16px] mt-[64px] overflow-y-auto z-10" :class="[isCompany ? 'grid-cols-1' : 'grid-cols-3']">
              <template v-if="isCompany">
                <button
                  v-for="company in companyList"
                  :key="company"
                  class="relative p-[24px] rounded-[16px] border-[4px] border-solid bg-white text-left"
                  :class="{ 'border-[#2C67EC]': activeChoose.firm === company, 'border-transparent': !(activeChoose.firm === company) }"
                  @click="handleChooseCompany(company)"
                >
                  <p class="text-[28px] font-medium">{{ company }}</p>
                  <div
                    v-if="activeChoose.firm === company"
                    class="absolute right-[-4px] top-[-4px] flex justify-center items-center w-[60px] h-[48px] bg-[#2C67EC] rounded-bl-[24px] rounded-tr-[16px]"
                  >
                    <img :src="CompanyActiveIcon" alt="company-active-icon" class="w-[30px] h-[20px]" />
                  </div>
                </button>
              </template>
              <template v-else>
                <button
                  v-for="department in departmentList"
                  :key="department.categoryId"
                  class="relative p-[24px] rounded-[16px] border-[4px] border-solid bg-white"
                  :class="{
                    'border-[#2C67EC]': activeChoose.categoryId === department.categoryId,
                    'border-transparent': !(activeChoose.categoryId === department.categoryId),
                  }"
                  @click="handleChooseDepartment(department)"
                >
                  <p class="text-[28px] font-medium">{{ department.center }}</p>
                  <div
                    v-if="activeChoose.categoryId === department.categoryId"
                    class="absolute right-[-4px] top-[-4px] flex justify-center items-center w-[60px] h-[48px] bg-[#2C67EC] rounded-bl-[24px] rounded-tr-[16px]"
                  >
                    <img :src="CompanyActiveIcon" alt="company-active-icon" class="w-[30px] h-[20px]" />
                  </div>
                </button>
              </template>
            </div>
            <button
              class="w-full h-[96px] bg-[#2C67EC] text-white text-[32px] font-medium rounded-[16px] mt-[64px]"
              @click="isCompany ? nextStep() : handleModalConfirm()"
            >
              {{ isCompany ? '下一步(1/2)' : '开始学习' }}
            </button>
          </div>
        </Transition>
      </div>
    </Transition>
  </Teleport>
</template>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
}

.modal-content {
  display: flex;
  flex-direction: column;
  padding: 64px 32px 32px 32px;
  width: 90%;
  max-height: 80%;
  position: relative;
  border: 2px solid #fff;
  border-radius: 32px;
  box-sizing: border-box;
  background: linear-gradient(221deg, #7ea6ff 0%, #81a2ed 34%, #e2ebff 100%);
  backdrop-filter: blur(60px);
}

/* 过渡动画 */
.modal-fade-enter-active,
.modal-fade-leave-active {
  transition: opacity 0.3s ease;
}

.modal-fade-enter-from,
.modal-fade-leave-to {
  opacity: 0;
}

.modal-slide-enter-active,
.modal-slide-leave-active {
  transition:
    transform 0.3s ease,
    opacity 0.3s ease;
}

.modal-slide-enter-from,
.modal-slide-leave-to {
  transform: translateY(30px);
  opacity: 0;
}
</style>
