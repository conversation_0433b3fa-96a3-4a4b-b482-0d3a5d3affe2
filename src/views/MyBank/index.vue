<script setup>
import { BankApi } from '@/api';
import { ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import NavBar from '@/components/NavBar.vue';
import ChooseCompany from './components/ChooseCompany.vue';
import EnterIcon from '@/assets/bank/enter.png';
import StarIcon from '@/assets/bank/star.png';
import WrongCollectIcon from '@/assets/bank/wrong-collect.png';
import { CollectTypeEnum } from './constant';

const router = useRouter();
const route = useRoute();

// =============== 公司与部门选择 ===============

const categoryId = ref('');
const showCatePopover = ref(false);
const showChooseCompanyModal = ref(false);

const refreshData = (chooseCategory) => {
  categoryId.value = chooseCategory.categoryId;
  finished.value = false;
  list.value = [];
  pageSize.value = 0;
  total.value = 0;
  getQuestionBank();
};

// =============== 列表 ===============

const loading = ref(false);
const finished = ref(false);
const list = ref([]);

const searchText = ref('');
const page = ref(1);
const pageSize = ref(0);
const total = ref(0);

// 获取题库列表
function getQuestionBank(isSearch = false) {
  if (!categoryId.value) {
    finished.value = true;
    return;
  }
  if (isSearch) {
    pageSize.value = 10;
  } else {
    pageSize.value += 10;
  }
  BankApi.getAllQuestionBank(page.value, pageSize.value, searchText.value, categoryId.value || '')
    .then((res) => {
      if (res.code === 0) {
        list.value = res.data.questionBankVos;
        total.value = res.data.total;
      } else {
        console.log(res.msg);
      }
    })
    .catch((err) => {
      console.log(err);
    })
    .finally(() => {
      loading.value = false;
      finished.value = total.value <= list.value.length;
    });
}

// 计算正确率
function getRightRate(item) {
  const { rightCount, finishCount } = item;
  if (finishCount === 0) {
    return 0;
  }
  return Math.ceil((rightCount / finishCount) * 100);
}

// =============== 更多入口 ===============

const showPopover = ref(false);

const actions = [
  { text: '收藏夹', icon: StarIcon },
  { text: '错题本', icon: WrongCollectIcon },
];

function handleSelect(_, index) {
  switch (index) {
    case 0:
      // 跳转到收藏夹
      router.push(`/bank-collect/${CollectTypeEnum.COLLECT}`);
      break;
    case 1:
      // 跳转到错题本
      router.push(`/bank-collect/${CollectTypeEnum.CORRECTION}`);
      break;
  }
}

// =============== 进入刷题页面 ===============

function handleClickStart(data) {
  router.push(`/bank-exam/index?bankId=${data.id}`);
}
</script>

<template>
  <div class="flex flex-col overflow-hidden bg-white">
    <!-- 标题栏 -->
    <nav-bar title="培训系统">
      <template #right>
        <div class="mr-3">
          <van-popover placement="left-start" v-model:show="showCatePopover" :overlay="true" trigger="manual" :close-on-click-outside="true">
            <div class="p-[24px] w-[300px] text-[28px]">
              <p>在此处可随时切换当前所选择的公司与部门</p>
              <p class="font-medium text-blue text-right" @click.stop="showCatePopover = false">明白了</p>
            </div>
            <template #reference>
              <img
                @click.stop="showChooseCompanyModal = !showChooseCompanyModal"
                class="w-[42px] h-[42px]"
                src="@/assets/bank/changeFirm.png"
                alt=""
              />
            </template>
          </van-popover>
        </div>

        <div>
          <van-popover placement="bottom-end" v-model:show="showPopover" :actions="actions" trigger="manual" @select="handleSelect">
            <template #reference>
              <van-icon name="ellipsis" size="25" @click.stop="showPopover = !showPopover" />
            </template>
          </van-popover>
        </div>
      </template>
    </nav-bar>

    <!-- 搜索 -->
    <div class="px-[32px] py-[16px] border-b border-solid border-[#EEEEEE]">
      <div class="flex items-center px-[24px] h-[80px] rounded-[16px] bg-[#F5F6F7]">
        <img src="@/assets/home/<USER>" alt="search" class="w-[30px] h-[30px]" />
        <input
          type="text"
          class="flex-1 text-[28px] ml-[16px] bg-transparent"
          @keypress.enter="getQuestionBank(true)"
          placeholder="搜索题库名称"
          v-model="searchText"
        />
      </div>
    </div>

    <!-- 题库列表 -->
    <div class="flex-1 overflow-y-auto" v-if="categoryId">
      <van-list v-model:loading="loading" :finished="finished" finished-text="没有更多了" @load="getQuestionBank">
        <div
          @click="handleClickStart(item)"
          v-for="item in list"
          :key="item.id"
          class="flex flex-col gap-[16px] m-[32px] px-[24px] py-[22px] bg-[#EEF1F6] rounded-[16px] card-bg"
        >
          <div class="flex items-center">
            <p class="flex-1 text-[28px] font-medium text-[#1D2129]/90 my-text-one-line-overflow">{{ item.name }}</p>
            <img :src="EnterIcon" alt="" class="w-[40px] h-[40px]" />
          </div>

          <div class="flex items-center gap-[16px]">
            <van-progress
              class="w-[240px]"
              :percentage="Math.ceil((item.finishCount / item.questionCount) * 100) || 0"
              stroke-width="10"
              :show-pivot="false"
              color="#2C67EC"
              track-color="#FFFFFF"
            />
            <div class="text-[24px] text-[#2C67EC] font-bold leading-[32px]">
              {{ item.questionCount ? Math.ceil((item.finishCount / item.questionCount) * 100) : 0 }}%
            </div>
            <span class="text-[24px] text-[#000000]/60">已练习</span>
            <span class="text-[24px] text-[#000000]/60">{{ item.finishCount }}/{{ item.questionCount }}</span>
          </div>

          <div class="flex items-center">
            <!-- TODO: 题型数量 -->
            <span class="bg-white rounded-[8px] px-[12px] py-[4px] text-[20px] text-[#000000]/60">单选题x4 · 多选题x26 · 判断题x20</span>
            <span class="ml-[8px] bg-white rounded-[8px] px-[12px] py-[4px] text-[20px] text-[#000000]/60">正确率: {{ getRightRate(item) }}%</span>
          </div>
        </div>
      </van-list>
    </div>
    <ChooseCompany v-model="showChooseCompanyModal" @submit="refreshData" />
  </div>
</template>

<style scoped>
.card-bg {
  background:
    url('@/assets/profile/bg.png') 100% 100% / 288px 288px no-repeat,
    linear-gradient(to bottom, #edf0f6, #f5f6f7);
}
</style>
