<script setup>
import { ref } from 'vue';
import { BankApi } from '@/api';
import { useRouter, useRoute } from 'vue-router';
import EnterIcon from '@/assets/bank/enter.png';

const router = useRouter();
const route = useRoute();


// =============== 收藏夹和错题本 ===============

const title = ref('');
const api = ref(null);

const type = route.params.type;

const TypeEnum = {
  COLLECT: 'collect', // 收藏夹
  CORRECTION: 'correction', // 错题本
};

(function getTitleInfo() {
  switch (type) {
    case TypeEnum.COLLECT:
      title.value = '收藏夹';
      api.value = BankApi.getCollectBank;
      break;
    case TypeEnum.CORRECTION:
      title.value = '错题本';
      api.value = BankApi.getWrongBank;
      break;
  }
})();

// =============== 列表 ===============

const loading = ref(false);
const finished = ref(false);
const list = ref([]);

const searchText = ref('');
const pageSize = ref(0);
const total = ref(0);

// 获取收藏夹列表
async function getQuestionBank(isSearch = false) {
  try {
    if (isSearch) {
      pageSize.value = 10;
    } else {
      pageSize.value += 10;
    }
    const res = await api.value(1, pageSize.value, searchText.value);
    if (res.code !== 0) {
      throw new Error(res.msg);
    }
    list.value = res.data.questionBankVos;
    total.value = res.data.total;
  } catch (err) {
    console.log(err);
  } finally {
    loading.value = false;
    finished.value = total.value <= list.value.length;
  }
}

// 点击题库
function handleClickStart(item) {
  if (type === TypeEnum.COLLECT) {
    router.push(`/bank-collect-list/${TypeEnum.COLLECT}/${item.id}`);
  } else {
    router.push(`/bank-collect-list/${TypeEnum.CORRECTION}/${item.id}`);
  }
}
</script>

<template>
  <div class="flex flex-col overflow-hidden bg-white">
    <!-- 标题栏 -->
    <nav-bar :title="title" />

    <!-- 搜索 -->
    <div class="px-[32px] py-[16px] border-b border-solid border-[#EEEEEE]">
      <div class="flex items-center px-[24px] h-[80px] rounded-[16px] bg-[#F5F6F7]">
        <img src="@/assets/home/<USER>" alt="search" class="w-[30px] h-[30px]" />
        <input
          type="text"
          class="flex-1 text-[28px] ml-[16px] bg-transparent"
          @keypress.enter="getQuestionBank(true)"
          :placeholder="`搜索${title}名称`"
          v-model="searchText"
        />
      </div>
    </div>

    <!-- 题库列表 -->
    <div class="flex-1 overflow-y-auto">
      <van-list v-model:loading="loading" :finished="finished" finished-text="没有更多了" @load="getQuestionBank">
        <div
          @click="handleClickStart(item)"
          v-for="item in list"
          :key="item.id"
          class="m-[32px] px-[24px] py-[22px] bg-[#EEF1F6] rounded-[16px]"
        >
          <div class="flex items-center">
            <p class="flex-1 text-[28px] font-medium text-[#1D2129]/90 my-text-one-line-overflow">{{ item.name }}</p>
            <img :src="EnterIcon" alt="" class="w-[40px] h-[40px]" />
          </div>

          <div class="flex items-center mt-[16px]">
            <span class="text-[24px] text-[#000000]/60">{{ (type === TypeEnum.COLLECT ? '已收藏 ' : '错题数量 ') + item.questionCount }}</span>
          </div>
        </div>
      </van-list>
    </div>
  </div>
</template>

<style scoped></style>
