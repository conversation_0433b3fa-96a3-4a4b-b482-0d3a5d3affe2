<script setup>
import { ref } from 'vue';
import { BankApi } from '@/api';
import { useRouter, useRoute } from 'vue-router';
import CollectIcon from '@/assets/bank/collect.png';
import QuestionTypeTag from '@/components/exam/QuestionTypeTag.vue';

const router = useRouter();
const route = useRoute();

// =============== 收藏夹和错题本 ===============

const api = ref(null);

const type = route.params.type;
const bankId = route.params.bankId;

const TypeEnum = {
  COLLECT: 'collect', // 收藏夹
  CORRECTION: 'correction', // 错题本
};

(function getApi() {
  switch (type) {
    case TypeEnum.COLLECT:
      api.value = BankApi.getCollectBank;
      break;
    case TypeEnum.CORRECTION:
      api.value = BankApi.getWrongBank;
      break;
  }
})();

// =============== 列表 ===============

const loading = ref(false);
const finished = ref(false);
const list = ref([]);

const searchText = ref('');
const pageSize = ref(0);
const total = ref(0);

// 获取收藏夹列表
async function getQuestionBank(isSearch = false) {
  try {
    if (isSearch) {
      pageSize.value = 10;
    } else {
      pageSize.value += 10;
    }
    const res = await api.value(1, pageSize.value, searchText.value);
    if (res.code !== 0) {
      throw new Error(res.msg);
    }
    list.value = res.data.questionBankVos;
    total.value = res.data.total;
  } catch (err) {
    console.log(err);
  } finally {
    loading.value = false;
    finished.value = total.value <= list.value.length;
  }
}

// 点击题库
function handleClickStart(item) {
  router.push(`/bank-exam/${type}?bankId=${bankId}&questionId=${item.id}`);
}
</script>

<template>
  <div class="flex flex-col overflow-hidden bg-white">
    <!-- 标题栏 -->
    <nav-bar :title="type === TypeEnum.COLLECT ? '收藏夹' : '错题本'" />

    <!-- 搜索 -->
    <div class="px-[32px] py-[16px] border-b border-solid border-[#EEEEEE]">
      <div class="flex items-center px-[24px] h-[80px] rounded-[16px] bg-[#F5F6F7]">
        <img src="@/assets/home/<USER>" alt="search" class="w-[30px] h-[30px]" />
        <input
          type="text"
          class="flex-1 text-[28px] ml-[16px] bg-transparent"
          @keypress.enter="getQuestionBank(true)"
          placeholder="搜索题目名称"
          v-model="searchText"
        />
      </div>
    </div>

    <!-- 题库列表 -->
    <div class="flex-1 overflow-y-auto bg-[#EEF1F6]/90">
      <van-list v-model:loading="loading" :finished="finished" finished-text="没有更多了" @load="getQuestionBank">
        <div @click="handleClickStart(item)" v-for="item in list" :key="item.id" class="m-[32px] px-[24px] py-[22px] bg-white rounded-[16px]">
          <div class="flex items-center justify-between">
            <QuestionTypeTag :type="item.type" />
            <img v-if="type === TypeEnum.COLLECT" :src="CollectIcon" alt="收藏" class="w-[48px] h-[48px]" />
          </div>
          <p class="mt-[16px] text-[28px] font-medium text-[#1D2129]/90">{{ item.name }}({{ item.score }}分)</p>
          <p v-if="type === TypeEnum.COLLECT" class="mt-[16px] text-[24px] text-[#000000]/60">收藏于 {{ item.createTime }}</p>
          <div v-else class="mt-[16px]">
            <span class="text-[24px] text-[#000000]/60">错误次数：{{ item.wrongCount }}</span>
            <span class="text-[24px] text-[#000000]/60">最近错误：{{ item.updateTime }}</span>
          </div>
        </div>
      </van-list>
    </div>
  </div>
</template>

<style scoped></style>
