<script setup>
import { ref, computed, watch, nextTick } from 'vue';
import { useRoute } from 'vue-router';
import { BankApi } from '@/api';
import BankExamNumberIcon from '@/assets/bank/bank-exam-number.png';
import BankExamMoreIcon from '@/assets/bank/bank-exam-more.png';
import RestartIcon from '@/assets/bank/restart.png';
import CollectIcon from '@/assets/bank/collect.png';
import unCollectIcon from '@/assets/bank/un-collect.png';
import ExamHeader from '@/components/exam/ExamHeader.vue';
import ExamQuestion from '@/components/exam/ExamQuestion.vue';
import ExamNumber from '@/components/exam/ExamNumber.vue';
import NavBar from '@/components/NavBar.vue';
import { QuestionModeEnum } from '@/components/exam/constant';
import { showConfirmDialog } from 'vant';

const route = useRoute();

const swipeRef = ref(null);
const bankId = route.query.bankId;
const defaultQuestionId = route.query.questionId;

// =============== 区分不同入口 ===============

const BankEnum = {
  INDEX: 'index',
  COLLECT: 'collect',
  CORRECTION: 'correction',
};

const getPageInfo = () => {
  let showCollectIcon = true;
  let getCardApi = null;
  let submitApi = null;
  let resetApi = null;

  console.log(200, route.params.type);
  

  switch (route.params.type) {
    case BankEnum.INDEX:
      getCardApi = BankApi.getQuestionBankAnswerCard;
      submitApi = BankApi.submitQuestion;
      resetApi = BankApi.resetQuestionRecord;
      break;
    case BankEnum.COLLECT:
      showCollectIcon = false;
      getCardApi = BankApi.getMarkBankCard;
      submitApi = BankApi.submitMarkQuestion;
      resetApi = BankApi.resetMarkBankRecord;
      break;
    case BankEnum.CORRECTION:
      getCardApi = BankApi.getWrongBankCard;
      submitApi = BankApi.submitQuestion;
      break;
  }
  return { showCollectIcon, getCardApi, submitApi, resetApi };
};

const { showCollectIcon, getCardApi, submitApi, resetApi } = getPageInfo();

// =============== 答题卡 ===============

const showExamNumber = ref(false);
const cardList = ref(null);
const allQuestionIds = ref([]);
const activeQuestion = ref('');

async function handleSelectQuestion(key) {
  activeQuestion.value = key;
  showExamNumber.value = false;
  await getQuestionListByCurrentIndex(key);

  // 滚动到新位置
  await nextTick();
  const swipeIndex = questionList.value.findIndex((q) => q.id === key);
  if (swipeIndex !== -1 && swipeRef.value) {
    swipeRef.value.swipeTo(swipeIndex, { immediate: true });
  }
}

// 获取答题卡信息
async function getAnswerCardInfo(isFirst = false) {
  try {
    console.log(100, getCardApi);
    
    const res = await getCardApi(bankId);
    if (res.code !== 0) throw new Error(res.msg);
    cardList.value = res.data;
    const keys = Object.keys(res.data);
    allQuestionIds.value = keys;
    if (isFirst && keys.length > 0) {
      activeQuestion.value = allQuestionIds.value.includes(defaultQuestionId) ? defaultQuestionId : keys[0];
    }
  } catch (err) {
    console.log(err.message);
  }
}

async function init() {
  await getAnswerCardInfo(true);
  await getQuestionList(0);
}
init();

// 监听 showExamNumber，每次打开时重新请求答题卡信息
watch(showExamNumber, (val) => {
  if (val) {
    getAnswerCardInfo();
  }
});

// =============== 试题列表 ===============

const questionList = ref([]);
const PRELOAD_WINDOW_SIZE = 50;
const FETCH_THRESHOLD = 5;

// 根据当前题目位置，获取试题列表

const getQuestionListByCurrentIndex = async (newId) => {
  if (!newId || allQuestionIds.value.length === 0) return;

  const currentIndex = allQuestionIds.value.indexOf(newId);
  if (currentIndex === -1) return;

  const firstLoadedId = questionList.value[0]?.id;
  const lastLoadedId = questionList.value[questionList.value.length - 1]?.id;

  let shouldFetch = true;

  // 如果试题总数小于等于预加载窗口大小，且已经加载了所有题目，则不需要重新获取
  if (allQuestionIds.value.length <= PRELOAD_WINDOW_SIZE && questionList.value.length === allQuestionIds.value.length) {
    shouldFetch = false;
  }
  // 当接近已加载列表的边缘时才重新获取
  else if (firstLoadedId && lastLoadedId) {
    const firstLoadedIndex = allQuestionIds.value.indexOf(firstLoadedId);
    const lastLoadedIndex = allQuestionIds.value.indexOf(lastLoadedId);

    const isWithinBounds = currentIndex >= firstLoadedIndex && currentIndex <= lastLoadedIndex;
    const isApproachingStart = currentIndex - firstLoadedIndex < FETCH_THRESHOLD && firstLoadedIndex > 0;
    const isApproachingEnd = lastLoadedIndex - currentIndex < FETCH_THRESHOLD && lastLoadedIndex < allQuestionIds.value.length - 1;

    // 如果在已加载范围内，并且没有接近边缘，则不执行任何操作
    if (isWithinBounds && !isApproachingStart && !isApproachingEnd) {
      shouldFetch = false;
    }
  }

  if (shouldFetch) {
    await getQuestionList(currentIndex);
  }
};

const getQuestionList = async (currentIndex) => {
  const start = Math.max(0, currentIndex - Math.floor(PRELOAD_WINDOW_SIZE / 2));
  const end = Math.min(allQuestionIds.value.length, start + PRELOAD_WINDOW_SIZE);
  const actualStart = end === allQuestionIds.value.length ? Math.max(0, end - PRELOAD_WINDOW_SIZE) : start;

  const questionIdsToFetch = allQuestionIds.value.slice(actualStart, end);

  try {
    const res = await BankApi.getQuestionListByIds(questionIdsToFetch);
    if (res.code !== 0) throw new Error(res.msg);
    const fetchedQuestions = res.data;
    const enrichedQuestions = fetchedQuestions.map((q) => {
      const cardInfo = cardList.value[q.id];
      if (cardInfo) {
        return {
          ...q,
          mode: QuestionModeEnum.READ,
          isRight: cardInfo.isRight,
          // TODO isUpdateAnswer
          // NOTE: checkAnswerChange function was in original code but not defined, so it's omitted here.
          myAnswer: cardInfo.answers?.join(),
        };
      }
      return { ...q, mode: QuestionModeEnum.WRITE };
    });

    questionList.value = questionIdsToFetch.map((id) => enrichedQuestions.find((q) => q.id === id)).filter(Boolean);
  } catch (err) {
    console.log(err.message);
  }
};

// 当前题目全局索引
const activeQuestionIndex = computed(() => {
  if (!activeQuestion.value || allQuestionIds.value.length === 0) return 0;
  return allQuestionIds.value.indexOf(activeQuestion.value) + 1;
});

// 滑动切换题目
function onSwipeChange(index) {
  const question = questionList.value[index];
  if (question && question.id !== activeQuestion.value) {
    activeQuestion.value = question.id;
    getQuestionListByCurrentIndex(question.id);
  }
}

// 提交答案
async function submitQuestion(answer) {
  try {
    const res = await submitApi(bankId, answer);
    if (res.code !== 0) throw new Error(res.msg);
    const question = questionList.value.find((item) => item.id === answer.questionId);
    if (question) {
      question.mode = QuestionModeEnum.READ;
      question.isRight = res.data.isRight;
      // TODO
      // question.isUpdateAnswer = checkAnswerChange(question, res.data);
      question.myAnswer = res.data.answers.join();
    }
  } catch (err) {
    console.log(err);
  }
}

// =============== 更多 ===============

const actions = [{ text: '重新刷题', icon: RestartIcon }];

const showPopover = ref(false);

function handleSelect(_, index) {
  switch (index) {
    case 0:
      resetBankExam();
      break;
  }
}

const resetBankExam = () => {
  showConfirmDialog({
    message: '确定要清空当前题库的答题记录,并重新开始刷题吗?',
  })
    .then(async () => {
      try {
        const res = await resetApi(bankId);
        if (res.code !== 0) throw new Error(res.msg);
        questionList.value = [];
        allQuestionIds.value = [];
        await getAnswerCardInfo(true);
        await getQuestionList(0);
      } catch (err) {
        console.log(err);
      }
    })
    .catch(() => {});
};

// =============== 收藏 ===============

async function setMark(bool, questionId) {
  try {
    const req = bool ? BankApi.markQuestion : BankApi.unmarkQuestion;
    const res = await req(bankId, questionId);
    if (res.code !== 0) {
      throw new Error(res.msg);
    }
    const question = questionList.value.find((q) => q.id === questionId);
    if (question) {
      question.isMark = bool;
    }
  } catch (err) {
    console.log(err);
  }
}
</script>

<template>
  <div class="flex flex-col overflow-hidden bg-white">
    <!-- 标题栏 -->
    <nav-bar>
      <template #right>
        <van-space :size="12">
          <img :src="BankExamNumberIcon" alt="" class="w-[48px] h-[48px]" @click="showExamNumber = true" />
          <van-popover placement="bottom-end" v-model:show="showPopover" :actions="actions" @select="handleSelect">
            <template #reference>
              <img :src="BankExamMoreIcon" alt="" class="w-[48px] h-[48px]" />
            </template>
          </van-popover>
        </van-space>
      </template>
    </nav-bar>

    <exam-header>
      <div class="text-[24px] text-[#1D2129]/90 font-medium leading-[48px]">
        <span class="text-[28px] text-[#2C67EC]">{{ activeQuestionIndex }}</span>
        <span>/{{ allQuestionIds.length }}</span>
      </div>
    </exam-header>

    <van-swipe ref="swipeRef" v-if="questionList.length" class="flex-1" :loop="false" :show-indicators="false" @change="onSwipeChange">
      <van-swipe-item v-for="question in questionList" :key="question.id">
        <exam-question :question="question" :immediate-submit="true" @select="submitQuestion">
          <template #right>
            <img
              :src="question.isMark ? CollectIcon : unCollectIcon"
              alt=""
              class="w-[48px] h-[48px]"
              @click="setMark(!question.isMark, question.id)"
            />
          </template>
        </exam-question>
      </van-swipe-item>
    </van-swipe>

    <exam-number v-model="showExamNumber" :card-list="cardList" :active-question="activeQuestion" @select="handleSelectQuestion" />
  </div>
</template>
