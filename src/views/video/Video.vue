<template>
  <div class="page">
    <div class="video">
      <PlayerVideo ref="playerRef" :lastPlayTimeR="lastPlayTimeR" @handleRotateFullscreen="handleRotateFullscreen"></PlayerVideo>
    </div>
    <Desc :video-title="videoTitle" :teachers="teachers"></Desc>
    <div class="panel">
      <div class="back" @click="handleBack" :class="{ isShow: isFullscreen }"></div>
      <div class="full-screen-back" @click="handleExitFullScreen" :class="{ isShow: !isFullscreen }"></div>

      <Catalogue :catalogue="catalogue" @handleChangeVideo="handleChangeVideo"></Catalogue>
    </div>
  </div>

  <Loading :isShow="loadingShow"></Loading>
  <AckDialog :ackShow="ackShow" :isFullscreen="isFullscreen" @closeAckDialog="closeAckDialog"></AckDialog>
</template>
<script setup>
import { onMounted, onUnmounted, ref, provide, nextTick } from 'vue';
import PlayerVideo from './childComps/PlayerVideo.vue';
// import PlayerVideo from './childComps/PlayerVideoMui.vue'
import Desc from './childComps/Desc.vue';
import Catalogue from './childComps/Catalogue.vue';
import AckDialog from './childComps/AckDialog.vue';

// import { showDialog } from 'vant';

import { useRouter, useRoute } from 'vue-router';
import { useCourseStore } from '@/store';
import { getUrl, getRecord } from '@/api/course.js';

const router = useRouter();
const route = useRoute();
const courseStore = useCourseStore();
provide('courseState', courseStore.currentCourseState);
provide('timeId', '');

const { id, title, chapter, courseId, timeId, teachers, duration } = route.query;
let videoTitle = ref(title);
let playerRef = ref(null);
const catalogue = ref(courseStore.currentCourseList);
let lastPlayTimeR = ref(null);
let loadingShow = ref(true);
// 视频确认弹窗show
let ackShow = ref(false);
const initData = () => {
  getUrl(id).then((url) => {
    let data = {
      chapter,
      courseId,
      fileId: id,
      timeId,
    };
    // 获取观看记录
    getRecord(data).then((res) => {
      lastPlayTimeR.value = res.sectionList[0].endPoint;
      loadingShow.value = false;
      // let aaa = 'https://vfx.mtime.cn/Video/2020/04/24/mp4/200424173514827105.mp4'
      // let bbb = 'https://vfx.mtime.cn/Video/2021/10/25/mp4/211025094126367108.mp4'
      // let ccc = 'https://vfx.mtime.cn/Video/2020/12/08/mp4/201208091010936577.mp4' // 45分钟
      let ddd =
        'https://ddbes-pan-1304188286.cos.ap-chongqing.myqcloud.com/video/training96e7180f7cb9456aaf21738c750e0866%E7%96%AF%E7%8B%82%E5%8A%A8%E7%89%A9%E5%9F%8E%20Zootopia%202016.mp4';
      playerRef.value.initPlayer(url, lastPlayTimeR.value);
    });
  });

  // 根据时长 随机生成弹窗出现时间
  if (duration > 120) {
    // 大于俩小时出现5次弹窗
    addSetTimeout(Math.ceil(Math.random() * 30));
    addSetTimeout(Math.ceil(Math.random() * 30) + 30);
    addSetTimeout(Math.ceil(Math.random() * 30) + 60);
    addSetTimeout(Math.ceil(Math.random() * 30) + 90);
    addSetTimeout(Math.ceil(Math.random() * duration));
  } else if (duration > 90) {
    // 1.5 - 2小时出现4次弹窗
    addSetTimeout(Math.ceil(Math.random() * 30));
    addSetTimeout(Math.ceil(Math.random() * 30) + 30);
    addSetTimeout(Math.ceil(Math.random() * 30) + 60);
    addSetTimeout(Math.ceil(Math.random() * duration));
  } else if (duration > 60) {
    // 1 - 1.5小时出现3次弹窗
    addSetTimeout(Math.ceil(Math.random() * 30));
    addSetTimeout(Math.ceil(Math.random() * 30) + 30);
    addSetTimeout(Math.ceil(Math.random() * duration));
  } else if (duration > 30) {
    // 0.5 - 1小时出现2次弹窗
    addSetTimeout(Math.ceil(Math.random() * 30));
    addSetTimeout(Math.ceil(Math.random() * duration));
  } else {
    // 小于半小时出现1次弹窗
    addSetTimeout(Math.ceil(Math.random() * duration));
  }
};
// 关闭ackDialog弹窗
const closeAckDialog = () => {
  ackShow.value = false;
  // 播放视频
  playerRef.value.controlPlayer(true);
};

// 定时器
function addSetTimeout(dur) {
  console.log('生成的时间是多少呢', dur);
  setTimeout(
    () => {
      ackShow.value = true;
      // 暂停视频播放
      playerRef.value && playerRef.value.controlPlayer(false);
    },
    dur * 60 * 1000,
  );
}

onMounted(() => {
  document.querySelector('.page').addEventListener(
    'touchmove',
    function (e) {
      if (e._isScroller) return;
      // 阻止默认事件
      console.log('阻止默认事件');

      e.preventDefault();
    },
    {
      passive: false,
    },
  );
});

onUnmounted(() => {
  console.log('播放页面销毁了');
});

const handleChangeVideo = ({ fileId, chapter, courseId, timeId, title }) => {
  videoTitle.value = title;
  playerRef.value.removeVideo();
  getUrl(fileId).then((url) => {
    let data = {
      chapter,
      courseId,
      fileId,
      timeId,
    };
    // 获取观看记录
    getRecord(data).then((res) => {
      lastPlayTimeR = res.sectionList[0].endPoint;
      console.log('观看记录', url, data);
      loadingShow.value = false;
      nextTick(function () {
        playerRef.value.initPlayer(url, lastPlayTimeR, data);
      });
    });
  });
};

// 点击了返回
const handleBack = () => {
  router.back();
};

let isFullscreen = ref(false);
// 进出入全屏
const handleRotateFullscreen = (bol) => {
  isFullscreen.value = bol;
};

// 点击返回，退出全屏
const handleExitFullScreen = () => {
  console.log('playerRef.value', playerRef.value);
  playerRef.value.exitFullScreen();
};

initData();
</script>

<style lang="scss" scoped>
.page {
  overflow: hidden;

  .panel {
    overflow: auto;
    background-color: rgb(245, 246, 247);
    height: 48vh;
    .back {
      width: 80px;
      height: 80px;
      position: absolute;
      top: 80px;
      left: 10px;
      background: url('@/assets/img/video-back.png') no-repeat center;
      // background-color: rgb(251, 0, 0);
    }
    .full-screen-back {
      width: 80px;
      height: 80px;
      position: absolute;
      top: 90px;
      right: 30px;
      background: url('@/assets/img/video-back.png') no-repeat center;
      transform: rotate(90deg);
    }
    .isShow {
      display: none;
    }
  }
}
</style>
