<template>
  <div class="box" v-show="ackShow">
    <div class="dialog" :class="{ rotated: isFullscreen }">
      <div class="img">
        <img src="@/assets/video/ack.webp" alt="" />
      </div>
      <div class="text">
        <h5>你还在看吗？</h5>
        <p>点击下方按钮继续播放视频吧</p>
      </div>
      <div class="line"></div>
      <div class="ack" @click="confirm">
        <span>确定</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const props = defineProps({
  ackShow: Boolean,
  isFullscreen: Boolean,
});
const emit = defineEmits(['closeAckDialog']);

const confirm = () => {
  console.log('点击了确定');
  emit('closeAckDialog');
};

const closeAckDialog = () => {
  emit('closeAckDialog');
};
</script>

<style lang="scss" scoped>
.box {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgb(0, 0, 0, 0.5);
  z-index: 9;
  height: 100px;
  display: flex;
  justify-content: center;
  align-items: center;

  .dialog {
    width: 320px;
    height: 360px;
    background: #ffffff;
    border-radius: 20px;
    display: flex;
    flex-direction: column;
    // justify-content: center;
    align-items: center;

    .img {
      margin-top: 31px;
      margin-bottom: 20px;
      img {
        width: 133px;
      }
    }
    .text {
      h5 {
        margin-bottom: 10px;
        font-size: 26px;
        font-family:
          PingFangSC-Regular,
          PingFang SC;
        font-weight: 400;
        color: #1a1a1a;
        line-height: 37px;
        letter-spacing: 1px;
        text-align: center;
      }
      p {
        text-align: center;
        width: 234px;
        font-size: 20px;
        font-family:
          PingFangSC-Regular,
          PingFang SC;
        font-weight: 400;
        color: #878b95;
      }
    }
    .line {
      width: 263px;
      border-top: 1px solid #dbdddf;
      margin-top: 12px;
      margin-bottom: 12px;
    }
    .ack {
      font-size: 26px;
      font-family:
        PingFangSC-Regular,
        PingFang SC;
      font-weight: 400;
      color: #23252a;
      line-height: 37px;
    }
  }
}

.rotated {
  transform: rotate(90deg);
}
</style>
