<template>
  <div class="catalogue">
    <div class="title">课程目录</div>
    <div class="section-list">
      <div class="section" v-for="(item, index) in catalogue" :key="index">
        <div class="right">
          <div class="sec-name" @click="goLook(item.appFileVoList[0], item.chapter, item.courseId)">{{ item.appFileVoList[0].name }}</div>
          <div class="sec-duration">
            <span>时长：{{ format(item.appFileVoList[0].duration) }}分钟</span>
          </div>
        </div>
        <div class="left">
          <img
            src="@/assets/detail/playVideo.png"
            v-if="courseState !== '未开课'"
            @click="goLook(item.appFileVoList[0], item.chapter, item.courseId)"
            alt=""
          />
          <img src="@/assets/detail/lock.png" v-else alt="" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { inject, ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';

import { useCourseStore } from '@/store';

const courseState = inject('courseState');
const props = defineProps({
  catalogue: Array,
});

const courseStore = useCourseStore();
const router = useRouter();
const emit = defineEmits(['handleChangeVideo']);

const timeId = courseStore.currentCourseTimeId;

// 点击了播放
const goLook = (fileItem, chapter, courseId) => {
  if (courseState.value === '未开课') return;
  let title = fileItem.name.split('.')[0];
  console.log('id', fileItem.id);

  let data = {
    fileId: fileItem.id,
    chapter,
    courseId,
    timeId: timeId,
    title,
  };

  emit('handleChangeVideo', data);
  // router.replace({
  //   path: '/video',
  //   query: {
  //     id: fileItem.id,
  //     title,
  //     chapter,
  //     courseId,
  //     timeId: timeId.value
  //   }
  // })
};
function format(duration) {
  // var minutes = parseInt((duration % (1000 * 60 * 60)) / (1000 * 60));
  let min = Math.floor(duration / (1000 * 60));

  return min;
}
</script>

<style lang="scss" scoped>
.catalogue {
  box-sizing: border-box;
  margin-top: 16px;
  width: 100%;
  background-color: #fff;
  padding-left: 42px;
  padding-top: 24px;
  .title {
    font-size: 30px;
    font-family:
      PingFangSC-Medium,
      PingFang SC;
    font-weight: 600;
    color: #23252a;
    margin-bottom: 26px;
  }
  .section-list {
    padding-bottom: 20px;
    .section {
      box-sizing: border-box;
      width: 666px;
      height: 121px;
      background: #f5f6f7;
      border-radius: 10px;
      padding-left: 32px;
      padding-top: 18px;
      display: flex;
      justify-content: space-between;
      border-bottom: 1px solid transparent;
      .right {
        .sec-name {
          font-size: 28px;
          width: 570px;
          font-family:
            PingFangSC-Regular,
            PingFang SC;
          font-weight: 400;
          color: #444956;
          margin-bottom: 11px;
          line-height: 40px;
          letter-spacing: 2px;
          // 设置文字溢出隐藏
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 1;
        }
        .sec-duration {
          font-size: 24px;
          font-family:
            PingFangSC-Regular,
            PingFang SC;
          font-weight: 400;
          color: #878b95;
          line-height: 33px;
        }
      }
      .left {
        line-height: 100px;
        margin-right: 27px;
        img {
          width: 34px;
        }
      }
    }
    .section + .section {
      margin-top: 16px;
    }
  }
}
</style>
