<template>
  <div class="desc">
    <h3 class="video-name">{{ videoTitle }}</h3>
    <div class="teacher">
      <img src="@/assets/base/person.png" alt="" />
      <span class="name">讲师&nbsp;&nbsp;{{ teachers }}</span>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
const props = defineProps({
  teachers: String,
  videoTitle: String,
});
</script>

<style lang="scss" scoped>
.desc {
  height: 130px;
  background-color: #fff;
  padding-top: 20px;
  padding-left: 30px;
  .video-name {
    //设置宽度，超过时用...表示
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
  .teacher {
    margin-top: 20px;
    img {
      width: 28px;
      margin-right: 10px;
      vertical-align: middle;
    }
    span {
      font-size: 28px;
      font-family:
        PingFangSC-Regular,
        PingFang SC;
      font-weight: 400;
      color: #444956;
      line-height: 40px;
      letter-spacing: 2px;
    }
  }
}
</style>
