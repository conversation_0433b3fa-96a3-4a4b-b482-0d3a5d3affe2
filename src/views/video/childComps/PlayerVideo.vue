<!--<template>-->
<!--  <div class="player">-->
<!--    <div id="mse"></div>-->
<!--  </div>-->
<!--  -->
<!--</template>-->

<!--<script setup>-->
<!--import { ref, onMounted, onUnmounted, nextTick } from 'vue'-->
<!--import { useRoute, useRouter } from 'vue-router'-->

<!--import Player from 'xgplayer/dist/core_player'-->
<!--import play from 'xgplayer/dist/controls/play'-->
<!--import fullscreen from 'xgplayer/dist/controls/fullscreen'-->
<!--import time from 'xgplayer/dist/controls/time'-->
<!--import volume from 'xgplayer/dist/controls/volume'-->
<!--import playbackRate from 'xgplayer/dist/controls/playbackRate'-->
<!--import memoryPlay from 'xgplayer/dist/controls/memoryPlay'-->
<!--import loading from 'xgplayer/dist/controls/loading'-->
<!--import progress from 'xgplayer/dist/controls/progress'-->

<!--import { getUrl, upRecord, getRecord } from '@/api/course.js'-->

<!--let urlAAA = 'http://clips.vorwaerts-gmbh.de/big_buck_bunny.mp4'-->

<!--const route = useRoute()-->
<!--const router = useRouter()-->
<!--const { id, title, chapter, courseId, timeId } = route.query-->

<!--const emit = defineEmits(['handleRotateFullscreen'])-->
<!--const props = defineProps({-->
<!--  lastPlayTimeR: Number-->
<!--})-->

<!--let lastPlayTime = ref()-->
<!--let player = null-->
<!--let timer = 0;-->
<!--let isFullscreen = false;// 是否是全屏-->
<!--let uploadfParams = ref({})-->

<!--let tarr = []-->
<!--let maxDuration = 0-->

<!--const initPlayer = (url, lastPlayTimeR, params = {}) => {-->
<!--  lastPlayTime.value = lastPlayTimeR-->
<!--  maxDuration = lastPlayTimeR / 1000-->
<!--  console.log('传来的params data&#45;&#45;&#45;&#45;', params);-->
<!--  if (params !== {}) {-->
<!--    uploadfParams.value.chapter = params.chapter-->
<!--    uploadfParams.value.courseId = params.courseId-->
<!--    uploadfParams.value.fileId = params.fileId-->
<!--    uploadfParams.value.timeId = params.timeId-->

<!--  }-->
<!--  // 初始化播放器-->
<!--  player = new Player({-->
<!--    // el、url为必选配置，其它为可选配置-->
<!--    id: 'mse',-->
<!--    url: url,-->
<!--    controlPlugins: [-->
<!--      play,-->
<!--      fullscreen,-->
<!--      volume,-->
<!--      playbackRate,-->
<!--      time,-->
<!--      memoryPlay,-->
<!--      loading,-->
<!--      progress-->
<!--    ],-->
<!--    // width: '100%',-->
<!--    width: 390,-->
<!--    height: 219,-->
<!--    autoplay: true, // 自动播放-->
<!--    playbackRate: [0.75, 1, 1.25, 1.5, 2], // 当前播放速度-->
<!--    playsinline: true, // 内联播放-->
<!--    rotateFullscreen: true, // 全屏横屏-->
<!--    cssFullscreen: true,-->
<!--    closeVideoClick: true, // 单击不暂停视频-->
<!--    lastPlayTime: lastPlayTimeR / 1000, //视频起播时间（单位：秒）-->
<!--    lastPlayTimeHideDelay: 3, //提示文字展示时长（单位：秒）-->
<!--    'x5-video-player-fullscreen': true,-->
<!--    // 'x5-video-orientation': 'landscape',-->
<!--    defaultPlaybackRate: 1 // 播放速度设置为1-->
<!--  })-->
<!--  player.once('ready', () => {-->
<!--    console.log('ready')-->
<!--    timer = setInterval(() => {-->
<!--      const qiepian =  player.getBufferedRange()-->
<!--      uploadf()-->
<!--    }, 10000)  -->
<!--  })-->
<!--  // 全屏时-->
<!--  player.on('getRotateFullscreen', () => {-->
<!--    console.log('进入样式横屏全屏 ');-->
<!--    isFullscreen = true-->
<!--    emit('handleRotateFullscreen', isFullscreen)-->
<!--  })-->
<!--  player.on('exitRotateFullscreen', () => {-->
<!--    console.log('退出样式横屏全屏');-->
<!--    isFullscreen = false;-->
<!--    emit('handleRotateFullscreen', isFullscreen)-->
<!--  })-->
<!--  -->
<!--  // 播放时间改变-->
<!--  player.on('timeupdate', (e) => {-->
<!--    if (player.currentTime > maxDuration) maxDuration = player.currentTime-->
<!--    tarr.unshift(player.currentTime)-->
<!--    tarr.length = 2-->
<!--  })-->
<!--  // seek播放-->
<!--  player.on('seeking', (e) => {-->
<!--    console.log('player.currentTime', player.currentTime);-->
<!--    if (player.currentTime > tarr[1]) {-->
<!--      if (player.currentTime > maxDuration) {-->
<!--        player.currentTime = tarr[1]-->
<!--      }-->
<!--    } -->
<!--  })-->
<!--  // 控制栏展示-->
<!--  player.on('controlShow', () => {-->
<!--    console.log('控制栏显现');-->
<!--  })-->
<!--  // 控制栏隐藏-->
<!--  player.on('controlHide', () => {-->
<!--    console.log('控制栏隐藏');-->
<!--  })-->
<!--  // 错误-->
<!--  player.on('error', (err) => {-->
<!--    console.log('错误了', err);-->
<!--  })-->
<!--  // waiting-->
<!--  player.on('waiting', () => {-->
<!--    console.log('等待中');-->
<!--  })-->
<!--  // 视频可以播放-->
<!--  player.on('canplay', () => {-->
<!--    console.log('视频可以播放');-->
<!--  })-->
<!--  // 视频可以播放-->
<!--  player.on('canplaythrough', () => {-->
<!--    console.log('视频可以流畅播放');-->
<!--  })-->
<!--}-->

<!--const exitFullScreen = () => {-->
<!--  console.log('退出全屏函数调用了', player);-->
<!--  player.exitRotateFullscreen()-->
<!--}-->

<!--// 暂停或者播放视频-->
<!--const controlPlayer = (bol) => {-->
<!--  console.log('现在是否全屏', player.fullscreen);-->
<!--  if (bol) {-->
<!--    -->
<!--    player.play()-->
<!--  } else {-->
<!--    player.pause()-->
<!--  }-->
<!--}-->

<!--onUnmounted(() => {-->
<!--  console.log('播放组件销毁了', player.currentTime)-->
<!--  clearInterval(timer)-->
<!--  uploadf()-->
<!--})-->

<!--// 上传播放记录-->
<!--function uploadf() {-->
<!--  if (!uploadfParams.value.chapter) {-->
<!--    uploadfParams.value.chapter = chapter-->
<!--    uploadfParams.value.courseId = courseId-->
<!--    uploadfParams.value.fileId = id-->
<!--    uploadfParams.value.timeId = timeId-->
<!--  }-->
<!--  -->
<!--  uploadfParams.value.startPoint = lastPlayTime.value-->
<!--  uploadfParams.value.endPoint = Math.floor(player.currentTime) * 1000-->
<!--  upRecord(uploadfParams.value).then((res) => {-->
<!--  })-->
<!--  lastPlayTime.value = Math.floor(player.currentTime) * 1000-->
<!--}-->

<!--// 销毁播放器-->
<!--const removeVideo = function() {-->
<!--  player.destroy()-->
<!--  clearInterval(timer)-->
<!--}-->

<!--defineExpose({-->
<!--  exitFullScreen,-->
<!--  initPlayer,-->
<!--  removeVideo,-->
<!--  controlPlayer-->
<!--})-->
<!--</script>-->

<!--<style lang="scss" scoped>-->
<!--</style>-->
<script setup></script>

<template>
  <div></div>
</template>
