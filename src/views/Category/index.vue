<script setup>
import { useRouter } from 'vue-router';
import { backClient } from '@/utils';
import { computed, ref } from 'vue';
import { HomeApi } from '@/api';
import { platform } from '@/config';

const router = useRouter();
const _platform = window.sessionStorage.getItem('__platform__');

// 分类信息
const categoryInfo = ref({});

// 选择公司与部门
const step = ref(1);
const checkedFirm = ref('');
const checkedCategory = ref({ center: '', categoryId: '' });
const checkDisable = computed(() => {
  if (step.value === 1) {
    return !checkedFirm.value;
  } else if (step.value === 2) {
    return !checkedCategory.value.center;
  } else {
    return false;
  }
});

const handleBack = () => {
  if (step.value === 1) {
    checkedFirm.value ? router.back() : backClient();
  } else {
    step.value = 1;
  }
};

const getCategory = () => {
  let category = localStorage.getItem('select_category') || sessionStorage.getItem('select_category');
  if (!category) return;
  category = JSON.parse(category);
  const { firm, center, categoryId } = category;
  if (!categoryInfo.value[firm]) return;
  checkedFirm.value = firm;
  if (!categoryInfo.value[firm].find((cate) => cate.categoryId === categoryId)) return;
  checkedCategory.value = { center, categoryId };
};

// 获取分类信息
const getCategoryInfo = async () => {
  try {
    const res = await HomeApi.getCategoryList();
    if (res.code === 0) {
      categoryInfo.value = res.data;
    } else {
      console.log(res.msg || '获取分类失败');
      router.back();
    }
  } catch (e) {
    console.log('获取分类失败');
    router.back();
  }
};

const handleFirmClick = (value) => {
  checkedFirm.value = value;
};

const handleChangeCategory = (center, categoryId) => {
  checkedCategory.value = { center, categoryId };
};

const handleSubmit = () => {
  if (checkDisable.value) return;
  if (step.value === 1) {
    step.value = 2;
  } else {
    const info = {
      firm: checkedFirm.value,
      ...checkedCategory.value,
    };
    if (_platform + '' === platform.ios) {
      window.webkit.messageHandlers.client_setStorage.postMessage(
        JSON.stringify({
          key: 'JS_storage',
          value: { select_category: info },
        }),
      );
    }
    localStorage.setItem('select_category', JSON.stringify(info));
    sessionStorage.setItem('select_category', JSON.stringify(info));
    router.back();
  }
};

const init = async () => {
  await getCategoryInfo();
  getCategory();
};

init();
</script>

<template>
  <div class="h-full flex flex-col overflow-hidden">
    <van-nav-bar :title="step === 1 ? '选择所在公司' : '选择所在部门'" left-arrow @click-left="handleBack" left-text="返回"></van-nav-bar>
    <div class="flex-1 overflow-auto py-[24px]">
      <div v-show="step === 1">
        <div v-for="firm in Object.keys(categoryInfo)" :key="firm" class="flex flex-col items-center mb-[24px]">
          <div
            class="flex justify-between items-center py-[52px] pl-[60px] pr-[36px] w-[688px] rounded-[20px] shadow bg-white border-blue"
            :class="checkedFirm === firm ? 'border' : 'border-none'"
            @click="handleFirmClick(firm)"
          >
            <span class="text-[32px]">{{ firm }}</span>
            <img v-if="checkedFirm === firm" class="w-[42px] h-[42px]" src="@/assets/category/check.png" alt="" />
            <img v-else class="w-[42px] h-[42px]" src="@/assets/category/uncheck.png" alt="" />
          </div>
        </div>
      </div>
      <div v-show="step === 2">
        <van-cell-group inset>
          <van-cell
            v-for="category in categoryInfo[checkedFirm]"
            :key="category.categoryId"
            :title="category.center"
            clickable
            @click="handleChangeCategory(category.center, category.categoryId)"
          >
            <template #right-icon>
              <img v-if="checkedCategory.categoryId === category.categoryId" class="w-[42px] h-[42px]" src="@/assets/category/check.png" alt="" />
              <img v-else class="w-[42px] h-[42px]" src="@/assets/category/uncheck.png" alt="" />
            </template>
          </van-cell>
        </van-cell-group>
      </div>
    </div>
    <div class="flex justify-center pt-[30px] h-[180px] bg-white">
      <div class="w-[580px] h-[100px] rounded-[15px] text-center" :class="checkDisable ? 'bg-bgBlue' : 'bg-blue'" @click="handleSubmit">
        <span class="text-[34px] text-white leading-[100px]">{{ step === 1 ? '确认选择' : '开始学习' }}</span>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
