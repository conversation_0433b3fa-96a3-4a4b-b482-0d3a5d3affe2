<script setup>
import { useRoute, useRouter } from 'vue-router';
import Tag from '@/components/Tag.vue';
import { ref, watch } from 'vue';

const router = useRouter();
const route = useRoute();
const { isRating } = route.query;

const back = () => {
  sessionStorage.removeItem('__exam_result__');
  router.back()
};

let data = sessionStorage.getItem('__exam_result__');

if (!data) {
  back();
}

data = JSON.parse(data);

/**
 * 试题列表
 */

const allList = data.planTestVo.planTestSections;
allList.forEach((section) => {
  section.questions.forEach((question) => {
    const wrong = data.planTestWrongRecords.find((wrong) => wrong.questionId === question.id);
    if (wrong) {
      question.wrongAnswers = wrong.wrongAnswers;
    }
  });
});

const rightList = [];
const wrongList = [];

allList.forEach((section) => {
  const wrongQuestions = section.questions.filter((question) => question.wrongAnswers);
  const rightQuestions = section.questions.filter((question) => !question.wrongAnswers);
  if (rightQuestions.length > 0) {
    rightList.push({ ...section, questions: rightQuestions });
  }
  if (wrongQuestions.length > 0) {
    wrongList.push({ ...section, questions: wrongQuestions });
  }
});

/**
 *  筛选
 */

const list = ref(allList);
const value1 = ref(0);
const option1 = [
  { text: '全部题目', value: 0 },
  { text: '只看错的', value: 1 },
  { text: '只看对的', value: 2 },
];

const handleDropDownChange = (value) => {
  switch (value) {
    case 0:
      list.value = allList;
      break;
    case 1:
      list.value = wrongList;
      break;
    case 2:
      list.value = rightList;
      break;
  }
};

/**
 * 查看详情
 */

const handleClickResult = () => {
  sessionStorage.setItem('__exam_analysis_info__', JSON.stringify({ ...data.planTestVo, planTestSections: allList }));
  router.push('/exam-test?mode=analysis');
};
</script>

<template>
  <div class="flex flex-col h-full overflow-hidden">
    <van-nav-bar title="考试结果" left-arrow @click-left="back" left-text="返回"></van-nav-bar>
    <div class="flex-1 overflow-auto">
      <!--  成绩  -->
      <div class="flex flex-col justify-center mb-[16px] h-[160px] bg-white pl-[40px]">
        <div class="flex mb-[10px] text-[28px] font-font font-normal text-simple">
          <span>你的得分: {{ data.userPlanTestRecord.score }}分</span>
          <div class="ml-[10px]" v-if="isRating === 'false'">
            <tag v-if="data.userPlanTestRecord.isPass" type="success" message="合格" />
            <tag v-else type="danger" message="不合格" />
          </div>
        </div>
        <div class="">
          <span class="text-[24px] font-font font-normal text-descColor">提交日期 {{ data.userPlanTestRecord.finishTime }}</span>
        </div>
      </div>
      <!--  结果  -->
      <div class="flex-1 flex flex-col bg-white">
        <van-dropdown-menu active-color="#0168FD">
          <van-dropdown-item v-model="value1" @change="handleDropDownChange" :options="option1" />
        </van-dropdown-menu>
        <div class="flex-1 p-[24px] overflow-auto">
          <div v-for="(section, index) in list" :key="index">
            <div class="ml-[10px] h-[60px] leading-[60px] my-text-one-line-overflow text-[28px]">
              {{ section.name }}
            </div>
            <div v-for="(question, j) in section.questions" :key="j" class="flex py-[23px]">
              <div class="ml-[11px] mr-[21px]">
                {{ question.wrongAnswers ? '❌' : '✅' }}
              </div>
              <span class="text-[28px] font-font font-normal text-simple my-text-one-line-overflow">{{ question.stem }}</span>
            </div>
          </div>
        </div>
      </div>
      <!--  查看解析  -->
      <div class="flex justify-center pt-[10px] h-[150px] bg-white">
        <div @click="handleClickResult" class="w-[580px] h-[100px] bg-blue text-center leading-[100px] rounded-[15px]">
          <span class="text-[34px] font-font font-medium text-white">查看解析</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
