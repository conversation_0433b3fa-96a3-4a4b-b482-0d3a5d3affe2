<script setup>
import { useRoute, useRouter } from 'vue-router';
import { computed, onMounted, onUnmounted, ref } from 'vue';
import StepQuestion from '@/components/test/StepQuestion.vue';
import QuestionAndAnalysis from '@/components/test/QuestionAndAnalysis.vue';
import { showConfirmDialog, showToast } from 'vant';
import { ExamApi } from '@/api';
import moment from 'moment';

const route = useRoute();
const router = useRouter();

const startTime = new Date().getTime();

function handleClickBack() {
  sessionStorage.removeItem('__exam_paper_info__');
  sessionStorage.removeItem('__exam_analysis_info__');
  router.back();
}

/** 获取试题信息 */

const { mode, id, isRelease, planTestId, isRating } = route.query;

let data;

if (mode === 'test') {
  data = sessionStorage.getItem('__exam_paper_info__');
} else if (mode === 'analysis') {
  data = sessionStorage.getItem('__exam_analysis_info__');
}

if (!data) {
  handleClickBack();
}

data = JSON.parse(data);

/**
 * 倒计时
 */

const time = ref(data.answerTime * 60);
let timeId;

const clock = computed(() => {
  const newTime = moment.duration(time.value, 'seconds');
  const minutes = newTime.minutes();
  const seconds = newTime.seconds();
  return `${minutes}:${seconds.toString().padStart(2, '0')}`;
});

if (mode === 'test') {
  const setTime = () => {
    timeId = setInterval(() => {
      if (time.value > 0) {
        time.value--;
        console.log(time.value);
      } else {
        clearInterval(timeId);
        handleSubmit();
      }
    }, 1000);
  };

  onMounted(() => {
    time.value && setTime();
  });

  onUnmounted(() => {
    clearInterval(timeId);
  });
}

/**
 * 试题
 */

const list = ref([]);

data.planTestSections.forEach((section) => {
  section.questions.forEach((question) => {
    list.value.push({
      ...question,
      sectionName: section.name,
      questionSectionId: section.id,
      mode: mode === 'test' ? 'write' : 'read',
    });
  });
});

const handleChangeAnswer = (questionId, question, answer) => {
  const changeQuestion = list.value.find((item) => item.id === questionId);
  if (changeQuestion) {
    changeQuestion.myAnswer = answer;
  }
};

/** 切换试题 */

const index = ref(0);
const showPopup = ref(false);

function handleChangeToLast() {
  if (index.value > 0) {
    index.value--;
  }
}

function handleChangeToNext() {
  if (index.value < list.value.length - 1) {
    index.value++;
  }
}

/**
 * 提交试卷
 */

const handleSubmit = async () => {
  try {
    const questionSubmits = list.value.map(({ type, id, myAnswer, questionSectionId }) => {
      return {
        answers: myAnswer ? myAnswer.split(',') : [],
        questionId: id,
        type,
        questionSectionId,
      };
    });
    const res = await ExamApi.submitExamPaper({
      planId: isRelease === 'true' ? '' : id,
      planTestReleaseId: isRelease === 'true' ? id : '',
      planTestId,
      questionSubmits,
      startTime,
      endTime: new Date().getTime(),
    });
    if (res.code !== 0) throw new Error(res.msg);
    console.log('res', res.data);
    sessionStorage.removeItem('__exam_paper_info__');
    sessionStorage.setItem('__exam_result__', JSON.stringify(res.data));
    router.replace(`/exam-result?isRating=${isRating}`);
  } catch (e) {
    showToast(e.message || '提交失败，请稍后重试');
  }
};

const handleSubmitCheck = () => {
  let tipStr = '提交后将立即生成考试成绩，确认提交吗？';
  if (list.value.some((item) => !item.myAnswer)) {
    tipStr = '您尚未完成全部试题， ' + tipStr;
  }
  showConfirmDialog({
    message: tipStr,
  })
    .then(async () => {
      await handleSubmit();
    })
    .catch(() => {
      // on cancel
    });
};
</script>

<template>
  <div class="h-full flex flex-col">
    <van-nav-bar :title="`试题 ${index + 1} / ${list.length}`" left-text="返回" left-arrow @click-left="handleClickBack">
      <template #right>
        <span v-if="mode === 'test'" class="text-blue" @click="handleSubmitCheck">提交</span>
      </template>
    </van-nav-bar>
    <div class="flex-1 overflow-auto">
      <div class="flex justify-between items-center h-[92px] px-[45px] border-b-[1px] bg-white text-[28px]">
        <div class="flex-1 my-text-one-line-overflow">
          {{ list[index].sectionName }}
        </div>
        <div v-if="mode === 'test'" class="flex items-center">
          <img src="@/assets/detail/date.png" class="mr-[9px] w-[40px] h-[40px]" alt="" />
          <span>{{ time > 0 ? clock : '不限时间' }}</span>
        </div>
      </div>
      <div
        v-if="list[index].wrongAnswers && list[index].wrongAnswers.join() === ''"
        class="ml-[50px] h-[60px] leading-[60px] text-[29px] text-descColor"
      >
        未作答
      </div>
      <!-- 题目选项及答案解析 -->
      <div v-if="list[index]" class="flex-1 overflow-scroll">
        <question-and-analysis
          :mode="list[index].mode"
          :index="index"
          :question="list[index]"
          @change="handleChangeAnswer"
          :isRight="!list[index].wrongAnswers"
          :myAnswer="list[index].wrongAnswers ? list[index].wrongAnswers.join() : list[index].answer"
        />
      </div>
    </div>
    <!--  导航  -->
    <step-question
      :index="index"
      :length="list.length"
      @handle-change-to-next="handleChangeToNext"
      @handle-change-to-last="handleChangeToLast"
      @show-popup="showPopup = true"
    />
    <!--  试题弹窗  -->
    <van-popup :show="showPopup" round position="bottom" class="flex flex-col h-3/5" @click-overlay="showPopup = false">
      <div class="my-[40px] text-center">
        <span class="text-[32px] font-font font-bold text-titleColor">答题卡</span>
      </div>
      <div class="grid grid-cols-7 justify-items-center pb-[50px] px-[30px]">
        <div
          v-for="(item, i) in list"
          :key="i"
          @click="
            () => {
              index = i;
              showPopup = false;
            }
          "
          class="flex justify-center items-center mb-[20px] border border-border rounded-[4px] w-[80px] h-[80px]"
          :style="{
            'border-color': mode === 'test' && list[i].myAnswer ? '#0168FD' : '#E4E9EF',
          }"
        >
          <div v-if="mode === 'analysis' && !list[i].wrongAnswers">✅</div>
          <div v-else-if="mode === 'analysis' && list[i].wrongAnswers">❌</div>
          <span
            v-else
            class="text-[35px] font-font font-normal text-simple"
            :style="{
              color: list[i].myAnswer ? '#0168FD' : '#000',
            }"
            >{{ i + 1 }}</span
          >
        </div>
      </div>
    </van-popup>
  </div>
</template>

<style scoped lang="scss"></style>
