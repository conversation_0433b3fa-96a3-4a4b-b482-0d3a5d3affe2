<script setup>
import { appBack } from '@/utils';
import { ExamApi } from '@/api';
import { ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import Tag from '@/components/Tag.vue';
import emptyImage from '@/assets/course/empty.png';
import { showToast } from 'vant';
import 'vant/es/toast/style';

const router = useRouter();
const route = useRoute();

// 考试状态
const tabActive = ref(0);

const handleTabChange = (key) => {
  tabActive.value = key;
  getExamList();
};

// 时间筛选
const showFilter = ref(false);
const currentDate = ref([new Date().getFullYear(), new Date().getMonth() + 1]);
const lastDate = ref([new Date().getFullYear(), new Date().getMonth() + 1]);

function handleFilterCancel() {
  currentDate.value = lastDate.value;
  showFilter.value = false;
}

function handleFilterConfirm() {
  lastDate.value = currentDate.value;
  getExamList();
  showFilter.value = false;
}

// 考试列表
const list = ref([]);

// 获取考试列表
const getExamList = async () => {
  try {
    const [year, month] = currentDate.value;
    const res = await ExamApi.getExamList(tabActive.value + 1, year, month);
    if (res.code !== 0) throw new Error(res.msg);
    list.value = checkPlanList(res.data);
  } catch (e) {
    showToast(e.message || '请求考试列表失败，请稍后重试');
  }
};

getExamList();

const checkPlanList = (list) => {
  const releaseExamList = list.filter((item) => item.release);                                                                                           
  const planExamList = list.filter((item) => !item.release);
  const planExamMap = {};
  planExamList.forEach((item) => {
    const planId = item.plan.id;
    if (planExamMap[planId]) {
      planExamMap[planId].exams.push(item);
    } else {
      planExamMap[planId] = { ...item.plan, exams: [item] };
    }
  });
  const planList = Object.values(planExamMap);
  if (releaseExamList.length > 0) planList.push({ id: '0', name: '单独发布的考试', exams: releaseExamList });
  return planList;
};

const checkStartingStatus = (timeStr) => {
  return new Date().getTime() < new Date(timeStr).getTime();
};

const checkIsEnd = (timeStr) => {
  return new Date().getTime() > new Date(timeStr).getTime();
};

// 进入考试
const handleGoToExam = (exam) => {
  const id = exam.release ? exam.releaseId : exam.planId;
  sessionStorage.setItem('__exam_info__', JSON.stringify(exam));
  router.push(`/exam-page?id=${id}&isRelease=${exam.release}`);
};

// 查看考试详情
const handleGoToDetail = async (exam) => {
  try {
    const id = exam.release ? '' : exam.planId;
    const releaseId = exam.release ? exam.releaseId : '';
    const planTestId = exam.planTestLinkVo.planTestId;
    const res = await ExamApi.getExamResult(id, planTestId, releaseId);
    if (res.code !== 0) throw new Error(res.msg);
    sessionStorage.setItem('__exam_result__', JSON.stringify(res.data));
    await router.push(`/exam-result?isRating=${exam.plan?.isRating}`);
  } catch (e) {
    showToast(e.message || '获取考试详情失败，请稍后重试');
  }
};

const handleToastNull = () => {
  showToast('未参加本次考试');
};
</script>

<template>
  <div class="flex flex-col overflow-hidden">
    <van-nav-bar title="培训系统" left-arrow @click-left="appBack(router, route)" left-text="返回">
      <template #right>
        <van-icon v-if="tabActive === 2" name="filter-o" size="20" @click="showFilter = true" />
      </template>
    </van-nav-bar>
    <van-tabs v-model:active="tabActive" @change="handleTabChange" title-active-color="#326FFF" color="#326FFF" class="mb-2">
      <van-tab title="进行中"></van-tab>
      <van-tab title="已完成"></van-tab>
      <van-tab title="已结束"></van-tab>
    </van-tabs>
    <div class="flex-1 bg-white px-[42px] overflow-auto">
      <div v-show="list.length === 0" class="mt-[200px]">
        <van-empty :image="emptyImage" description="暂无考试信息" image-size="120" />
      </div>
      <div v-for="plan in list" :key="plan.id">
        <div class="py-[24px] text-[30px] text-titleColor font-medium">
          <span>{{ plan.name }}</span>
        </div>
        <div v-for="exam in plan.exams" :key="exam.planTestLinkVo.planTestId">
          <div class="rounded-[10px] bg-grey p-[24px] mb-[10px]">
            <div class="flex justify-between items-center pb-[16px]">
              <span class="text-[28px]">{{ exam.planTestLinkVo.testName }}</span>
              <div v-if="exam.userPlanTestRecord?.isFinish">
                <tag v-if="exam.plan?.isRating" type="primary" message="已完成"></tag>
                <tag v-else-if="exam.userPlanTestRecord.isPass" type="success" message="合格"></tag>
                <tag v-else type="danger" message="不合格"></tag>
              </div>
              <div v-else>
                <tag v-if="checkIsEnd(exam.planTestLinkVo.endTime)" type="danger" message="未考试"></tag>
                <tag v-else-if="!checkStartingStatus(exam.planTestLinkVo.startTime)" type="primary" message="考试开始"></tag>
                <tag v-else type="default" message="暂未开始"></tag>
              </div>
            </div>
            <div class="text-[24px] text-descColor">
              <span v-if="exam.userPlanTestRecord?.isFinish">提交时间{{ exam.userPlanTestRecord.finishTime }}</span>
              <span v-else-if="checkStartingStatus(exam.planTestLinkVo.startTime)">开始时间{{ exam.planTestLinkVo.startTime }}</span>
              <span v-else>结束时间 {{ exam.planTestLinkVo.endTime }}</span>
            </div>
            <div class="my-[15px] border-t-[1px]"></div>
            <div class="flex justify-between items-center">
              <div class="text-[24px] text-descColor">
                <span class="mr-[24px]">总分 {{ exam.planTestLinkVo.totalScore }}</span>
                <span v-if="exam.userPlanTestRecord?.isFinish">得分 {{ exam.userPlanTestRecord.score }}</span>
              </div>
              <div v-if="exam.userPlanTestRecord?.isFinish" class="btn">
                <span @click="handleGoToDetail(exam)">查看详情</span>
              </div>
              <div v-else-if="!checkIsEnd(exam.planTestLinkVo.endTime) && !checkStartingStatus(exam.planTestLinkVo.startTime)" class="btn">
                <span @click="handleGoToExam(exam)">进入考试</span>
              </div>
              <div class="btn" v-else-if="checkIsEnd(exam.planTestLinkVo.endTime)">
                <span @click="handleToastNull">查看详情</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 时间筛选 -->
    <van-popup v-model:show="showFilter" round position="bottom" :style="{ height: '50%' }">
      <van-date-picker
        v-model="currentDate"
        title="选择年月"
        :min-date="new Date(2022, 0, 1)"
        :max-date="new Date(2050, 0, 1)"
        :columns-type="['year', 'month']"
        @cancel="handleFilterCancel"
        @confirm="handleFilterConfirm"
      />
    </van-popup>
  </div>
</template>

<style scoped lang="scss">
.btn {
  @apply flex justify-center items-center w-[107px] h-[50px] bg-blue rounded-[20px] text-[20px] text-white;
}
</style>
