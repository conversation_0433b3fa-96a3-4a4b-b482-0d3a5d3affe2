<script setup>
import { useRoute, useRouter } from 'vue-router';
import { computed, ref } from 'vue';
import { showToast } from 'vant';
import { ExamApi } from '@/api';

const route = useRoute();
const router = useRouter();

const { id, isRelease } = route.query;

const back = () => {
  sessionStorage.removeItem('__exam_info__');
  router.back();
};

// 当前考试信息
let currentExam = sessionStorage.getItem('__exam_info__');

if (!currentExam) {
  showToast('考试信息不存在，请重新选择考试');
  back();
} else {
  currentExam = JSON.parse(currentExam);
}

const { testName, planTestId, totalScore, passScore, answerTime, questionNum, sectionNum } = currentExam.planTestLinkVo;

const examInfo = computed(() => {
  const info = [
    { label: '题目数量', value: `共计${sectionNum || 0}大题${questionNum || 0}小题` },
    { label: '考试总分', value: `${totalScore || 0}分` },
    {
      label: '答卷时长',
      value: `${answerTime ? answerTime + '分钟' : '不限时长'}`,
    },
  ];

  if (!currentExam.plan?.isRating) {
    info.splice(2, 0, { label: '及格分数', value: `${passScore || 0}分` });
  }

  return info;
});

const isLoading = ref(false);

// 进入考试页面
const handleGoToTest = async () => {
  try {
    isLoading.value = true;
    const res = await ExamApi.getExamPaper(id, planTestId, isRelease);
    if (res.code !== 0) throw new Error(res.msg);
    sessionStorage.removeItem('__exam_info__');
    sessionStorage.setItem('__exam_paper_info__', JSON.stringify(res.data));
    router.replace(`/exam-test?mode=test&id=${id}&planTestId=${planTestId}&isRelease=${isRelease}&isRating=${currentExam.plan?.isRating}`);
  } catch (e) {
    showToast(e.message || '请求考试试卷失败，请稍后重试');
  } finally {
    isLoading.value = false;
  }
};
</script>

<template>
  <div>
    <van-nav-bar title="考试" left-arrow @click-left="back" left-text="返回"></van-nav-bar>

    <div class="px-[92px]">
      <div class="mt-[50px] text-[40px]">{{ testName || '考试' }}</div>
      <div class="mt-[80px] mb-[120px]">
        <div v-for="item in examInfo" :key="item.label" class="mb-[16px] text-[30px]">
          <span class="mr-[10px] text-descColor">{{ item.label }}: </span>
          <span class="text-simple">{{ item.value }}</span>
        </div>
      </div>
      <button :disabled="isLoading" @click="handleGoToTest" class="h-[100px] w-full bg-blue rounded-[15px] leading-[100px] text-center">
        <span class="text-[34px] font-font font-medium text-white">进入考试</span>
      </button>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
