<script setup>
import { useRoute, useRouter } from 'vue-router';
import { appBack } from '@/utils';
import { BankApi } from '@/api';
import BankEnum from './enum';
import { ref } from 'vue';

const router = useRouter();
const route = useRoute();

const resetApi = ref(null);
const type = ref('');

(function () {
  switch (route.params.type) {
    case BankEnum.INDEX:
      resetApi.value = BankApi.resetQuestionRecord;
      type.value = BankEnum.INDEX;
      break;
    case BankEnum.COLLECT:
      resetApi.value = BankApi.resetMarkBankRecord;
      type.value = BankEnum.COLLECT;
      break;
  }
})();

/** 获取数据 */

let data = sessionStorage.getItem('__bank_test_info__');
data = data ? JSON.parse(data) : null;

console.log(data);

if (!data) {
  handleClickBack();
}

// 计算正确率
function getRightRate() {
  const { rightCount, finishCount } = data;
  if (finishCount === 0) {
    return 0;
  }
  return Math.ceil((rightCount / finishCount) * 100);
}

/** 返回 */

function handleClickBack() {
  appBack(router, route);
}

/** 跳转进入刷题页面 */

function getButtonText() {
  if (data.isPass === true) {
    return '重新刷题';
  } else if (data.finishCount === 0) {
    return '开始刷题';
  } else {
    return '继续刷题';
  }
}

function handleClickStart() {
  if (data.isPass === true) {
    resetApi
      .value(data.id)
      .then((res) => {
        if (res.code === 0) {
          router.push(`/bank-test/${type.value}`);
        } else {
          console.log(res.msg);
        }
      })
      .catch((err) => {
        console.log(err);
      });
  } else {
    router.push(`/bank-test/${type.value}`);
  }
}
</script>

<template>
  <div class="h-full flex flex-col bg-white">
    <!-- 标题栏 -->
    <van-nav-bar title="题库" left-text="返回" left-arrow @click-left="handleClickBack" />
    <div class="flex-1 flex flex-col items-center px-[50px]">
      <!-- 题库标题 -->
      <div class="mb-[20px] w-full h-[92px] leading-[92px] my-text-one-line-overflow">
        {{ data.name }}
      </div>
      <!-- 数据展示 -->
      <div class="w-full mb-[60px]">
        <van-row class="h-[80px] w-full">
          <van-col span="6" class="leading-[80px]">
            <span class="text-[32px] font-font font-normal text-descColor">题目数量: </span>
          </van-col>
          <van-col span="18" class="leading-[80px]">
            <span class="text-[32px] font-font font-normal text-simple">{{ data.questionCount }} 道</span>
          </van-col>
        </van-row>
        <van-row class="h-[80px] w-full">
          <van-col span="6" class="leading-[80px]">
            <span class="text-[32px] font-font font-normal text-descColor">刷题数量: </span>
          </van-col>
          <van-col span="18" class="leading-[80px]">
            <span class="text-[32px] font-font font-normal text-simple">{{ data.finishCount }} 道</span>
          </van-col>
        </van-row>
        <van-row class="h-[80px] w-full">
          <van-col span="6" class="leading-[80px]">
            <span class="text-[32px] font-font font-normal text-descColor">正确率: </span>
          </van-col>
          <van-col span="18" class="leading-[80px]">
            <span class="text-[32px] font-font font-normal text-simple">{{ getRightRate() }} %</span>
          </van-col>
        </van-row>
      </div>
      <!-- 操作按钮 -->
      <div v-show="data.questionCount > 0" @click="handleClickStart()" class="w-[580px] h-[100px] bg-blue rounded-[15px] leading-[100px] text-center">
        <span class="text-[34px] font-font font-medium text-white">{{ getButtonText() }}</span>
      </div>
    </div>
  </div>
</template>
