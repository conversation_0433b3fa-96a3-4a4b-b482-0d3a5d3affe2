<script setup>
import { useRoute, useRouter } from 'vue-router';
import { computed, ref, watch } from 'vue';
import { Bank<PERSON><PERSON> } from '@/api';
import QuestionAndAnalysis from '@/components/test/QuestionAndAnalysis.vue';
import StepQuestion from '@/components/test/StepQuestion.vue';
import BankEnum from './enum';

const router = useRouter();
const route = useRoute();

const showCollectIcon = ref(true);
const getCardApi = ref(null);
const submitApi = ref(null);
const backStep = ref(-1);

function getPageInfo() {
  switch (route.params.type) {
    case BankEnum.INDEX:
      getCardApi.value = BankApi.getQuestionBankAnswerCard;
      submitApi.value = BankApi.submitQuestion;
      backStep.value = -2;
      break;
    case BankEnum.COLLECT:
      showCollectIcon.value = false;
      getCardApi.value = BankApi.getMarkBankCard;
      submitApi.value = BankApi.submitMarkQuestion;
      backStep.value = -2;
      break;
    case BankEnum.CORRECTION:
      getCardApi.value = BankApi.getWrongBankCard;
      submitApi.value = BankApi.submitQuestion;
      backStep.value = -1;
      break;
  }
}

/** 获取题库信息 */

let data = sessionStorage.getItem('__bank_test_info__');
data = data ? JSON.parse(data) : null;

console.log(data);

if (!data) {
  handleClickBack();
}

/** 返回 */

function handleClickBack() {
  router.go(backStep.value);
}

/** 题目列表 */

const list = ref([]);

function getQuestionList(status) {
  const keys = getReqQuestionKeys();
  BankApi.getQuestionListByIds(keys)
    .then((res) => {
      if (res.code === 0) {
        // 按答题卡顺序排序
        const data = keys.map((key) => res.data.find((item) => item.id === key));
        // 确定读还是写
        data.forEach((item) => {
          const cardInfo = Object.values(cardList.value).find((res) => res && res.questionId === item.id);
          if (cardInfo) {
            item.mode = 'read';
            item.isRight = cardInfo.isRight;
            item.isUpdateAnswer = checkAnswerChange(item, cardInfo);
            item.myAnswer = cardInfo.answers.join();
          } else {
            item.mode = 'write';
          }
        });
        list.value = data;
        baseIndex.value = markSection.value.minValue;
        setQuestionIndex(status);
      } else {
        console.log(res.msg);
      }
    })
    .catch((err) => {
      console.log(err);
    });
}

function getReqQuestionKeys() {
  const { list } = markSection.value;
  return list.map((i) => cardListKeys.value[i]);
}

function checkIsRight(question, cardInfo) {
  let result;
  switch (question.type) {
    case 'SINGLE':
    case 'T_OR_F':
      result = question.answer === cardInfo.answers[0];
      break;
    case 'MULTI':
      result = question.answer.split(',').sort().join() === cardInfo.answers.sort().join();
      break;
  }
  return result;
}

function checkAnswerChange(question, cardInfo) {
  return cardInfo.isRight ? !checkIsRight(question, cardInfo) : checkIsRight(question, cardInfo);
}

/** 切换题目 */

const questionIndex = ref(0);

const index = computed(() => markSection.value.minValue + questionIndex.value);

function handleChangeToNext() {
  // 如果是最后一道题目，不做处理
  if (index.value === cardListKeys.value.length - 1) {
    return;
  }
  const newIndex = index.value + 1;
  multiAnswer.value = null;
  const { maxValue } = markSection.value;
  if (newIndex > maxValue) {
    markIndex.value += 10;
    getQuestionList('next');
  } else {
    questionIndex.value++;
  }
}

function handleChangeToLast() {
  // 如果是第一道题目，不做处理
  if (index.value === 0) {
    return;
  }
  const newIndex = index.value - 1;
  multiAnswer.value = null;
  const { minValue } = markSection.value;
  if (newIndex < minValue) {
    markIndex.value -= 10;
    getQuestionList('last');
  } else {
    questionIndex.value--;
  }
}

function setQuestionIndex(status) {
  switch (status) {
    case 'next':
      questionIndex.value = 0;
      break;
    case 'last':
      questionIndex.value = markSection.value.maxValue - markSection.value.minValue;
      break;
    case 'position':
      questionIndex.value = markIndex.value - markSection.value.minValue;
      break;
  }
}

/** 答题卡 */

const cardList = ref({});
const cardListKeys = computed(() => Object.keys(cardList.value));
const markIndex = ref(4);
const showPopup = ref(false);

function handleShowPopup(bool) {
  getAnswerCardInfo();
  showPopup.value = bool;
}

// 获取答题卡信息
function getAnswerCardInfo(isMountedReq = false) {
  getCardApi
    .value(data.id)
    .then((res) => {
      if (res.code === 0) {
        cardList.value = res.data;
        if (isMountedReq) {
          getQuestionList('next');
        }
      } else {
        console.log(res.msg);
      }
    })
    .catch((err) => {
      console.log(err);
    });
}

// 获取答题卡请求区间
function getMarkSection() {
  let minIndex = markIndex.value - 4;
  let maxIndex = markIndex.value + 5;
  minIndex = minIndex < 0 ? 0 : minIndex;
  maxIndex = maxIndex > cardListKeys.value.length - 1 ? cardListKeys.value.length - 1 : maxIndex;
  const keyList = [];
  for (let i = minIndex; i < maxIndex + 1; i++) {
    keyList.push(i);
  }
  return { list: keyList, minValue: minIndex, maxValue: maxIndex };
}

const markSection = computed(() => getMarkSection());

// 点击答题卡切换题目
function handleCardClick(i) {
  if (index.value === i) {
    return;
  }
  multiAnswer.value = null;
  const { list } = markSection.value;
  if (!list.includes(i)) {
    markIndex.value = i;
    getQuestionList('position');
  } else {
    questionIndex.value = i - markSection.value.minValue;
  }
  showPopup.value = false;
}

/** 标题 */

const baseIndex = ref(0);

function getTitle() {
  const questionCount = cardListKeys.value.length;
  return baseIndex.value + questionIndex.value + 1 + '/' + questionCount;
}

/** 答题 */

const multiAnswer = ref(null);

function handleChangeAnswer(questionId, question, answer) {
  if (question.type === 'SINGLE' || question.type === 'T_OR_F') {
    submitQuestion(questionId, question.type, answer);
  } else if (question.type === 'MULTI') {
    multiAnswer.value = { questionId, question, answer };
  }
}

function handleSubmitMultiAnswer() {
  if (multiAnswer.value === null) return;
  const { questionId, question, answer } = multiAnswer.value;
  submitQuestion(questionId, question.type, answer);
}

function submitQuestion(questionId, type, answer) {
  const answerArr = answer.split(',');
  submitApi
    .value(data.id, { answers: answerArr, questionId, type })
    .then((res) => {
      if (res.code === 0) {
        const question = list.value.find((item) => item.id === questionId);
        if (question) {
          question.mode = 'read';
          question.isRight = res.data.isRight;
          question.isUpdateAnswer = checkAnswerChange(question, res.data);
          question.myAnswer = res.data.answers.join();
        }
      } else {
        console.log(res.msg);
      }
    })
    .catch((err) => {
      console.log(err);
    });
}

/** 收藏 */

function setMark(bool) {
  const req = bool ? BankApi.markQuestion : BankApi.unmarkQuestion;
  req(data.id, list.value[questionIndex.value].id)
    .then((res) => {
      if (res.code === 0) {
        list.value[questionIndex.value].isMark = bool;
      } else {
        console.log(res.msg);
      }
    })
    .catch((err) => {
      console.log(err);
    });
}

/** 初始化 */

(function () {
  getPageInfo();
  getAnswerCardInfo(true);
})();
</script>

<template>
  <div class="h-full flex flex-col bg-grey overflow-hidden">
    <!-- 标题栏 -->
    <van-nav-bar :title="getTitle()" left-text="返回" left-arrow @click-left="handleClickBack">
      <template #right>
        <div v-if="showCollectIcon && list[questionIndex]">
          <van-icon v-if="list[questionIndex].isMark" @click="setMark(false)" name="star" size="18" />
          <van-icon v-else @click="setMark(true)" name="star-o" size="18" />
        </div>
      </template>
    </van-nav-bar>
    <!-- 题目选项及答案解析 -->
    <div v-if="list[questionIndex]" class="flex-1 overflow-scroll">
      <question-and-analysis
        :mode="list[questionIndex].mode"
        :index="baseIndex + questionIndex"
        :question="list[questionIndex]"
        @change="handleChangeAnswer"
        :isRight="list[questionIndex].isRight"
        :myAnswer="list[questionIndex].myAnswer"
      />
    </div>
    <div v-else class="flex-1"></div>
    <!-- d多选题目确定按钮-->
    <div v-if="list[questionIndex] && list[questionIndex].type === 'MULTI' && list[questionIndex].mode === 'write'">
      <div @click="handleSubmitMultiAnswer" class="mx-auto mb-[40px] w-[580px] h-[100px] bg-blue rounded-[15px] text-center leading-[100px]">
        <span class="text-[34px] font-font font-normal text-white">确定</span>
      </div>
    </div>
    <!--  导航  -->
    <step-question
      :index="index"
      :length="cardListKeys.length"
      @handle-change-to-next="handleChangeToNext"
      @handle-change-to-last="handleChangeToLast"
      @show-popup="handleShowPopup"
    />
    <!-- 答题卡 -->
    <van-popup :show="showPopup" round position="bottom" class="flex flex-col h-3/5" @click-overlay="showPopup = false">
      <div class="my-[40px] text-center">
        <span class="text-[32px] font-font font-bold text-titleColor">答题卡</span>
      </div>
      <div class="flex-1 px-[10px] overflow-auto">
        <div class="grid grid-cols-6 gap-y-[24px] justify-items-center">
          <div v-for="(item, key, i) in cardList" :key="key" @click="handleCardClick(i)">
            <div
              class="w-[76px] h-[76px] rounded-full bg-[#F5F6FA] border text-center leading-[76px]"
              :class="{
                'bg-bgBlue text-blue border-blue': i === index,
                'bg-blue text-white border-blue': item && item.isRight,
                'bg-orange text-white': item && !item.isRight,
              }"
            >
              <span>{{ i + 1 }}</span>
            </div>
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>
