<script setup>
import { appBack } from '@/utils';
import { useRoute, useRouter } from 'vue-router';
import { ref } from 'vue';
import { showToast } from 'vant';
import { PersonApi } from '@/api';
import { clickCard } from './utils.ts';

const router = useRouter();

const route = useRoute();

const searchText = ref('');

const year = ref([new Date().getFullYear()]);

const activeTab = ref(true);

const scoresData = ref({});

function handleSearch(value) {
  searchText.value = value;
  getMyScores();
}

async function getMyScores() {
  try {
    const res = await PersonApi.getCourseScore(year.value[0], activeTab.value, searchText.value);

    if (res.code !== 0) {
      throw new Error(res.msg);
    }

    scoresData.value = res.data;
  } catch (e) {
    showToast(e.message);
  }
}

function getTime(dateStr) {
  const date = new Date(dateStr);
  return `${date.getMonth() + 1}月${date.getDate()}日`;
}

getMyScores();

/**
 * picker
 */

const showPicker = ref(false);

const minDate = new Date(2023, 0, 1);

const maxDate = new Date();

function formatter(type, option) {
  if (type === 'year') {
    option.text += '年';
  }
  return option;
}

function onConfirm({ selectedValues }) {
  year.value = selectedValues;
  getMyScores();
  showPicker.value = false;
}
</script>

<template>
  <div class="h-full flex flex-col bg-grey overflow-hidden">
    <van-nav-bar title="我的成绩" left-arrow left-text="返回" @click-left="appBack(router, route)">
      <template #right>
        <van-icon @click="showPicker = true" name="filter-o" size="18" />
      </template>
    </van-nav-bar>

    <van-search v-model="searchText" @search="handleSearch" placeholder="搜索课程名称、讲师" />

    <div class="pb-2 border-b bg-white">
      <van-tabs v-model:active="activeTab" type="card" shrink @change="getMyScores">
        <van-tab title="合格" :name="true"></van-tab>
        <van-tab title="不合格" :name="false"></van-tab>
      </van-tabs>
    </div>

    <div class="flex-1 pl-[40px] pr-[35px] bg-white overflow-auto">
      <div v-for="month in Object.keys(scoresData).reverse()" :key="month" class="pt-3 pb-1 border-b last:border-b-0">
        <div class="mb-3 text-[28px] text-descColor">{{ year[0] }}年{{ month }}月</div>
        <div class="mb-2" v-for="course in scoresData[month]" :key="course.courseId" @click="clickCard(router, course)">
          <div class="relative flex mb-3">
            <div class="relative w-[240px] h-[176px] mr-[32px] overflow-hidden bg-amber-100">
              <img class="w-full h-full rounded-[10px]" :src="course.thumb" alt="" />
            </div>
            <div class="flex-1 flex flex-col">
              <div class="h-[95px] text-[30px] font-font font-normal text-titleColor my-text-two-lines-overflow">
                {{ course.courseName }}
              </div>
              <div class="flex items-center mb-[12px] w-[300px]">
                <img src="@/assets/comps/course-panel/category.png" class="w-[28px] h-[28px] mr-[16px]" alt="" />
                <span class="text-[26px] font-font font-normal text-descColor my-text-one-line-overflow">{{ course.categoryName || '未分类' }}</span>
              </div>
              <div class="flex items-center w-[250px]">
                <img src="@/assets/comps/course-panel/user.png" class="w-[28px] h-[28px] mr-[16px]" alt="" />
                <span class="text-[26px] font-font font-normal text-descColor my-text-one-line-overflow">讲师 {{ course.teacher }}</span>
              </div>
            </div>
            <div class="absolute right-0 bottom-0 text-[26px] font-font font-normal text-descColor">
              {{ getTime(course.startTime) }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <van-popup v-model:show="showPicker" round position="bottom">
      <van-date-picker
        v-model="year"
        :columns-type="['year']"
        :formatter="formatter"
        :min-date="minDate"
        :max-date="maxDate"
        @cancel="showPicker = false"
        @confirm="onConfirm"
      />
    </van-popup>
  </div>
</template>

<style scoped lang="scss"></style>
