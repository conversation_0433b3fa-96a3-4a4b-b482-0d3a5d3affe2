<script setup>
import { appBack } from '@/utils';
import { useRoute, useRouter } from 'vue-router';
import { ref } from 'vue';
import { DetailApi, PersonApi } from '@/api';
import { showImagePreview, showToast } from 'vant';
import CourseFileItem from '@/views/Details/comps/CourseFileItem.vue';
import { getPureImageUrl } from '@/utils/image';

const route = useRoute();

const router = useRouter();

const showPicker = ref(false);

const searchText = ref('');

const year = ref([new Date().getFullYear()]);

const attachmentData = ref({});

const minDate = new Date(2023, 0, 1);

const maxDate = new Date();

function handleSearch(value) {
  console.log(searchText.value);
  searchText.value = value;
  getMyAttachment();
}

async function getMyAttachment() {
  try {
    const res = await PersonApi.getMyAttachment(searchText.value, year.value[0]);

    if (res.code !== 0) {
      throw new Error(res.msg);
    }

    attachmentData.value = res.data;
  } catch (e) {
    showToast(e.message);
  }
}

function formatter(type, option) {
  if (type === 'year') {
    option.text += '年';
  }
  return option;
}

function onConfirm({ selectedValues }) {
  year.value = selectedValues;
  showPicker.value = false;
  getMyAttachment();
}

async function play(file) {
  try {
    const { id, name, extension } = file;
    const res = await DetailApi.getFileUrl(id);
    if (res.code !== 0) throw new Error(res.msg);

    switch (extension) {
      case 'doc':
      case 'docx':
      case 'xls':
      case 'xlsx':
      case 'ppt':
      case 'pptx':
      case 'pdf':
        sessionStorage.setItem('__preview_url__', res.data);
        router.push(`/file-preview/${name}`);
        break;
      case 'mp4':
      case 'avi':
      case 'rmvb':
      case 'rm':
      case 'flv':
      case 'mov':
      case 'mkv':
      case 'wmv':
      case '3gp':
      case 'mpg':
      case 'mpeg':
      case 'm4v':
        sessionStorage.setItem('__player_url__', res.data);
        break;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
        showImagePreview([getPureImageUrl(res.data)]);
        return;
    }
  } catch (e) {
    showToast(e.message);
  }
}

getMyAttachment();
</script>

<template>
  <div class="h-full flex flex-col bg-grey overflow-hidden">
    <van-nav-bar title="我的附件" left-arrow left-text="返回" @click-left="appBack(router, route)">
      <template #right>
        <van-icon @click="showPicker = true" name="filter-o" size="18" />
      </template>
    </van-nav-bar>

    <van-search v-model="searchText" @search="handleSearch" placeholder="搜索附件名称" />

    <div class="flex-1 pl-[40px] pr-[35px] bg-white overflow-auto">
      <div v-for="month in Object.keys(attachmentData).reverse()" :key="month" class="pt-3 pb-1 border-b last:border-b-0">
        <div class="mb-3 text-[28px] text-descColor">{{ year[0] }}年{{ month }}月</div>
        <div class="mb-2" v-for="attachment in attachmentData[month]" :key="attachment.id" @click="play(attachment)">
          <course-file-item :data="attachment" />
        </div>
      </div>
    </div>

    <van-popup v-model:show="showPicker" round position="bottom">
      <van-date-picker
        v-model="year"
        :columns-type="['year']"
        :formatter="formatter"
        :min-date="minDate"
        :max-date="maxDate"
        @cancel="showPicker = false"
        @confirm="onConfirm"
      />
    </van-popup>
  </div>
</template>

<style scoped lang="scss"></style>
