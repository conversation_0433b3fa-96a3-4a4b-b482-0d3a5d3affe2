<script setup>
import { appBack } from '@/utils';
import { useRoute, useRouter } from 'vue-router';
import { ref } from 'vue';
import { PersonApi } from '@/api';
import { showToast } from 'vant';
import Tag from '@/components/Tag.vue';
import { formatTimeToMinutes } from '../../utils/utils';
import { useSessionStorage } from '@vueuse/core';

const router = useRouter();

const route = useRoute();

const searchText = useSessionStorage('completedCourse_SearchText', '');

const activeTab = useSessionStorage('completedCourse_ActiveTab', 1);

const courseMap = ref({ today: [], yesterday: [], earlier: [] });

const courseTitleMap = {
  today: '今天',
  yesterday: '昨天',
  earlier: '更早',
};

function handleSearch(value) {
  searchText.value = value;
  getCompletedCourse();
}

async function getCompletedCourse() {
  try {
    const res = await PersonApi.getCompletedCourse(searchText.value, activeTab.value);
    if (res.code !== 0) {
      throw new Error(res.msg);
    }

    courseMap.value = res.data;
  } catch (e) {
    showToast(e.message);
  }
}

function getLastStudyTime(type, lastTime) {
  const date = new Date(lastTime);
  switch (type) {
    case 'today':
    case 'yesterday':
      return `${date.getHours()}:${date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()}`;
    case 'earlier':
      return `${date.getMonth() + 1}月${date.getDate()}日`;
  }
}

function clickCard(router, course) {
  const { planId, plan, courseId, isFinish } = course;
  if (!planId) {
    router.push(`/details/${courseId}`);
    return;
  }

  if (new Date(plan.endTime) < new Date()) {
    router.push(`/details/${courseId}/${planId}?isFinish=${isFinish}`);
  } else {
    router.push(`/details/${courseId}/${planId}`);
  }
}

getCompletedCourse();
</script>

<template>
  <div class="h-full flex flex-col bg-grey overflow-hidden">
    <van-nav-bar title="我看过的" left-arrow left-text="返回" @click-left="appBack(router, route)"></van-nav-bar>

    <van-search v-model="searchText" @search="handleSearch" placeholder="搜索课程名称、讲师" />

    <div class="pb-2 bg-white">
      <van-tabs v-model:active="activeTab" @change="getCompletedCourse" type="card" shrink>
        <van-tab title="计划内" :name="1"></van-tab>
        <van-tab title="计划外" :name="2"></van-tab>
      </van-tabs>
    </div>

    <div class="flex-1 px-4 bg-white overflow-auto">
      <div v-for="key in Object.keys(courseMap)" :key="key">
        <div class="py-1 mb-1 font-semibold bg-white sticky top-0 z-10">{{ courseTitleMap[key] }}</div>
        <div v-for="course in courseMap[key]" :key="course.courseId + course.planId" @click="clickCard(router, course)">
          <div class="relative flex mb-3">
            <div class="relative w-[240px] h-[176px] mr-[32px] overflow-hidden bg-amber-100">
              <img class="w-full h-full rounded-[10px]" :src="course.thumb" alt="" />
              <tag v-if="course.finishDuration >= course.totalDuration" class="absolute bottom-0 right-0" type="tip" message="已看完"></tag>
              <tag
                v-else
                class="absolute bottom-0 right-0"
                type="tip"
                :message="`${formatTimeToMinutes(course.finishDuration)}/${formatTimeToMinutes(course.totalDuration)}`"
              ></tag>
            </div>
            <div class="flex-1 flex flex-col">
              <div class="h-[95px] text-[30px] font-font font-normal text-titleColor my-text-two-lines-overflow">
                {{ course.courseName }}
              </div>
              <div class="flex items-center mb-[12px] w-[300px]">
                <img src="@/assets/comps/course-panel/category.png" class="w-[28px] h-[28px] mr-[16px]" alt="" />
                <span class="text-[26px] font-font font-normal text-descColor my-text-one-line-overflow">{{ course.categoryName || '未分类' }}</span>
              </div>
              <div class="flex items-center w-[250px]">
                <img src="@/assets/comps/course-panel/user.png" class="w-[28px] h-[28px] mr-[16px]" alt="" />
                <span class="text-[26px] font-font font-normal text-descColor my-text-one-line-overflow">讲师 {{ course.teacher }}</span>
              </div>
            </div>
            <div class="absolute right-0 bottom-0 text-[26px] font-font font-normal text-descColor">
              {{ getLastStudyTime(key, course.lastStudyTime) }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
