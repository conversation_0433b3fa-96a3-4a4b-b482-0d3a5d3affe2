<script setup>
import { backClient } from '@/utils';
import { useRouter } from 'vue-router';
import Segmented from '@/views/PersonPage/comps/Segmented.vue';
import Statistic from '@/views/PersonPage/comps/Statistic.vue';
import ListItem from '@/views/PersonPage/comps/ListItem.vue';
import FileIcon from '@/assets/person/file.png';
import { dateList } from './config';
import { computed, ref } from 'vue';
import { PersonApi } from '@/api';
import { formatTimeToHour } from '@/utils/utils';
import ChartCard from './comps/ChartCard.vue';

const router = useRouter();

const timeType = ref(dateList[0].type);
const timeValue = ref(1);
const personData = ref({});
const studyDuration = computed(() => formatTimeToHour(personData.value.totalDuration, false));

function changeSegmented(id) {
  timeType.value = id;
}

async function getPersonData() {
  try {
    const res = await PersonApi.getPersonData(timeType.value, timeValue.value);
    if (res.code !== 0) {
      throw new Error(res.msg);
    }
    personData.value = res.data;
  } catch (e) {
    console.log(e);
  }
}

function changeTimeValue(value) {
  timeValue.value = value;
  getPersonData();
}
</script>

<template>
  <div class="h-full flex flex-col bg-grey overflow-hidden">
    <van-nav-bar title="培训系统" left-arrow left-text="返回" @click-left="backClient"></van-nav-bar>

    <div class="flex-1 p-[30px] overflow-auto text-[24px]">
      <div class="px-[20px]">
        <segmented @change="changeSegmented" />
      </div>

      <div class="flex justify-center my-[30px]">
        <statistic width="100%" :data="[`${studyDuration.hour}`, '小时', `${studyDuration.min}`, '分钟']" desc="学习时长" />
      </div>

      <div class="flex justify-center">
        <statistic
          class="border-r"
          :show-icon="true"
          :data="[`${personData.planNum || 0}`, '节']"
          desc="看过"
          @click="router.push('/completed-course')"
        />
        <statistic class="border-r" :data="[`${personData.studyDay || 0}`, '天']" desc="学习天数" />
        <statistic :show-icon="true" :data="[`${Math.ceil(personData.completion) || 0}`, '%']" desc="合格率" @click="router.push('/my-scores')" />
      </div>

      <chart-card :time-type="timeType" :data="personData.studyTimes" @change="changeTimeValue" />

      <div class="px-[12px]">
        <list-item class="mb-2" :image="FileIcon" @click="router.push('/my-attachment')">
          <div class="relative">
            <span>我的附件</span>
            <!--  <div class="absolute top-[-100%] right-[-100%] rounded-full w-[12px] h-[12px] bg-deepOrange"></div>-->
          </div>
        </list-item>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
