<script setup>
import { dateList } from '@/views/PersonPage/config';
import * as echarts from 'echarts/core';
import { GridComponent } from 'echarts/components';
import { BarChart } from 'echarts/charts';
import { CanvasRenderer } from 'echarts/renderers';
import { onMounted, ref, watch } from 'vue';
import { formatStudyTimes } from '../config';
import TimePicker from '@/views/PersonPage/comps/TimePicker.vue';

echarts.use([GridComponent, Bar<PERSON>hart, CanvasRenderer]);

const props = defineProps({
  timeType: {
    type: Number,
    required: true,
  },
  data: {
    type: Array,
    default: () => {
      return [];
    },
  },
});

const emits = defineEmits(['change']);

const chartRef = ref(null);

let chart = null;

const options = {
  grid: {
    top: '10%', // 设置图表上方的 padding
    bottom: '10%', // 设置图表下方的 padding
    left: '5%', // 设置图表左边的 padding
    right: '12%', // 设置图表右边的 padding
  },
  xAxis: {
    type: 'category',
    axisLine: {
      lineStyle: {
        color: '#F3F4F8',
      },
    },
    axisLabel: {
      color: '#23252A',
      formatter: '{value}',
    },
    axisTick: {
      show: false, // 不显示刻度线
    },
    data: [],
  },
  yAxis: {
    type: 'value',
    position: 'right',
    axisLabel: {
      color: '#878B95',
      formatter: '{value}h',
    },
    splitLine: {
      lineStyle: {
        type: 'dashed',
        color: '#F3F4F8',
        width: 1,
      },
    },
  },
  series: [
    {
      data: [],
      type: 'bar',
      itemStyle: {
        borderRadius: [4, 4, 4, 4], // 设置柱子的圆角
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: '#408DFF' }, // 顶部颜色
          { offset: 1, color: '#65C7FF' }, // 底部颜色
        ]),
      },
    },
  ],
};

watch([() => props.data, () => props.timeType], () => {
  if (chartRef.value === null) return;
  const { xData, seriesData, xAxisLabelFormatter } = formatStudyTimes(props.timeType, props.data);
  options.xAxis.data = xData;
  options.series[0].data = seriesData;
  options.xAxis.axisLabel.formatter = xAxisLabelFormatter;
  chart.setOption(options);
});

onMounted(() => {
  if (chartRef.value === null) return;
  chart = echarts.init(chartRef.value);
});
</script>

<template>
  <div class="my-[20px] p-[32px] bg-white rounded-[24px]">
    <div class="mb-[12px] text-[30px] font-medium">学习时长分布</div>
    <div class="flex justify-between items-center">
      <div class="text-[24px] text-descColor">{{ dateList.find((date) => date.type === timeType)?.studyTimeTitle }}</div>
      <time-picker v-show="timeType !== 0" :key="timeType" :time-type="timeType" @change="(value) => emits('change', value)" />
    </div>
    <div ref="chartRef" class="h-[500px] w-full"></div>
  </div>
</template>

<style scoped lang="scss"></style>
