<script setup>
import { ref } from 'vue';
import { getOptionsFromStudyTimes } from '@/views/PersonPage/config';

const props = defineProps({
  timeType: {
    type: Number,
    required: true,
  },
});

const emits = defineEmits(['change']);

const showPopup = ref(false);

const pickerData = getOptionsFromStudyTimes(props.timeType);

const pickerValue = ref([pickerData.defaultValue]);

const pickerTitle = ref(pickerData.defaultText);

function changePicker({ _, selectedOptions }) {
  const { text, value } = selectedOptions[0];
  pickerTitle.value = text;
  emits('change', value);
  showPopup.value = false;
}

emits('change', pickerData.defaultValue);
</script>

<template>
  <div>
    <div class="text-descColor" @click="showPopup = !showPopup">
      <span>{{ pickerTitle }}</span>
      <van-icon class="ml-1" name="arrow-down" />
    </div>
    <van-popup v-model:show="showPopup" round position="bottom">
      <van-picker v-model="pickerValue" :title="pickerData.title" :columns="pickerData.options" @confirm="changePicker" />
    </van-popup>
  </div>
</template>

<style scoped lang="scss"></style>
