<script setup>
defineProps({
  width: {
    type: String,
    default: '30%',
  },
  showIcon: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Array,
    default: () => {
      return [];
    },
  },
  desc: {
    type: String,
    default: '',
  },
});
</script>

<template>
  <div class="relative flex flex-col items-center text-[24px]" :style="{ width: width }">
    <div class="relative">
      <span v-for="(item, i) in data" :key="i" :class="i % 2 === 0 ? 'text-[48px] font-bold' : 'text-[24px]'">{{ data[i] }}</span>
    </div>
    <span class="text-descColor">{{ desc }}</span>
    <van-icon v-if="showIcon" style="position: absolute; top: 50%; right: 10px" name="arrow" :size="10" />
  </div>
</template>

<style scoped lang="scss"></style>
