<script setup>
defineProps({
  image: {
    type: Object,
    required: true,
  },
});
</script>

<template>
  <div class="flex items-center p-[32px] rounded-[20px] bg-white">
    <img :src="image" class="mr-2 w-[42px] h-[42px]" alt="" />
    <div class="flex-1 text-[30px] font-medium">
      <slot />
    </div>
    <van-icon class="w-[27px] h-[27px] text-descColor" name="arrow" />
  </div>
</template>

<style scoped lang="scss"></style>
