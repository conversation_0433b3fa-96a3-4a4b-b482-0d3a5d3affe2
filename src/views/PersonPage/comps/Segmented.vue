<script setup>
import { onMounted, ref } from 'vue';
import { dateList } from '../config';

const emits = defineEmits(['change']);

const list = dateList.map((date) => ({ id: date.type, name: date.title }));

const index = ref(0);

function handleClick(i, item) {
  index.value = i;
  emits('change', item.id);
}

onMounted(() => {
  emits('change', list[0].id);
});
</script>

<template>
  <div class="flex p-[7px] rounded-[30px] bg-[#EBECEE]">
    <div
      v-for="(item, i) in list"
      :key="item.id"
      @click="handleClick(i, item)"
      class="w-1/4 py-[6px] text-center rounded-[24px]"
      :class="index === i ? 'text-black bg-white' : 'text-descColor'"
    >
      <span>{{ item.name }}</span>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
