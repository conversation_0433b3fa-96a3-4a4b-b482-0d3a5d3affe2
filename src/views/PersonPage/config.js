import { getWeekOfYear, getWeeksOfYear } from '@/utils/utils';

export const dateList = [
  {
    title: '周',
    type: 1,
    studyTimeTitle: '本周每日学习时长',
  },
  {
    title: '月',
    type: 2,
    studyTimeTitle: '本月每周学习时长',
  },
  {
    title: '年',
    type: 3,
    studyTimeTitle: '本年每月学习时长',
  },
  {
    title: '总',
    type: 0,
    studyTimeTitle: '年度学习时长',
  },
];

const weekTitle = ['日', '一', '二', '三', '四', '五', '六'];

function formatMillisecondToHour(millisecond) {
  let hour = millisecond / 1000 / 60 / 60;
  return hour > 0 ? Math.max(0.1, Number(hour.toFixed(1))) : 0;
}

export function formatStudyTimes(type, studyTimes) {
  return {
    xData: type === 1 ? studyTimes.map((item) => weekTitle[new Date(item.subscript).getDay()]) : studyTimes.map((item) => item.subscript),
    seriesData: studyTimes.map((item) => formatMillisecondToHour(item.duration)),
    xAxisLabelFormatter: type === 2 ? '第{value}周' : '{value}',
  };
}

export function getOptionsFromStudyTimes(type) {
  switch (type) {
    case 1:
      return {
        title: '选择周',
        options: Array.from({ length: getWeeksOfYear(new Date().getFullYear()).weekNumber }, (_, index) => ({
          text: `第${index + 1}周`,
          value: index + 1,
        })),
        defaultValue: getWeekOfYear(new Date()),
        defaultText: `第${getWeekOfYear(new Date())}周`,
      };
    case 2:
      return {
        title: '选择月',
        options: Array.from({ length: 12 }, (_, index) => ({
          text: `${index + 1}月`,
          value: index + 1,
        })),
        defaultValue: new Date().getMonth() + 1,
        defaultText: `${new Date().getMonth() + 1}月`,
      };
    case 3:
      return {
        title: '选择年',
        options: Array.from({ length: 5 }, (_, index) => ({
          text: `${new Date().getFullYear() - index}年`,
          value: new Date().getFullYear() - index,
        })),
        defaultValue: new Date().getFullYear(),
        defaultText: `${new Date().getFullYear()}年`,
      };
    default:
      return {
        defaultValue: 0,
      };
  }
}
