<template>
  <NavBar :title="'培训系统'"></NavBar>
  <div class="panel">
    <Swiper class="swiper">
      <!-- <SwiperItme v-for="(item, i) in imgList" :key="i">
        <img :src="item" alt="" class="s-img" />
      </SwiperItme> -->
      <SwiperItme>
        <img src="@/assets/main/img1.png" alt="" class="s-img" />
      </SwiperItme>
      <SwiperItme>
        <img src="@/assets/main/img2.png" alt="" class="s-img" />
      </SwiperItme>
    </Swiper>

    <div class="title-bar" v-if="MyCourse.length > 0">
      <TitleBar :title="'上课提醒'" :message-num="MyCourse.length">
        <span @click="lookAll"> 查看全部 </span>
        <img class="search" src="@/assets/base/go.png" alt="" />
      </TitleBar>
    </div>

    <van-swipe class="my-swipe" :show-indicators="false" indicator-color="white">
      <van-swipe-item v-for="item in MyCourse" :key="item.id">
        <Curriculum :course-data="item"></Curriculum>
      </van-swipe-item>
    </van-swipe>

    <div class="title-bar">
      <TitleBar :title="'学习时长'"></TitleBar>
    </div>

    <Surface @changeLoading="changeLoading"></Surface>
    <TabBar></TabBar>
  </div>
  <Loading :isShow="loadingShow"></Loading>
</template>

<script>
// import { ref, onMounted } from 'vue'
// import { useRouter, onBeforeRouteEnter } from 'vue-router'
// import { onBeforeRouteLeave, onBeforeRouteUpdate } from 'vue-router'
// import { Dialog } from 'vant'

import Swiper from '@/components/Swiper.vue';
import SwiperItme from '@/components/SwiperItem.vue';
import TabBar from '@/components/TabBar.vue';
import TitleBar from '@/components/TitleBar.vue';
import Curriculum from '../../components/Curriculum.vue';
import Surface from './childComps/Surface.vue';

import { getMyCourse, getStudyTime } from '@/api/course.js';
import { platform, nameSpace } from '@/config';
import Bus from '@/utils/bus.js';
import { getCourseState } from '@/utils/index.js';
export default {
  data() {
    return {
      // imgList: [
      //   'https://fastly.jsdelivr.net/npm/@vant/assets/apple-1.jpeg',
      //   'https://fastly.jsdelivr.net/npm/@vant/assets/apple-2.jpeg',
      //   'https://fastly.jsdelivr.net/npm/@vant/assets/apple-3.jpeg',
      //   'https://fastly.jsdelivr.net/npm/@vant/assets/apple-2.jpeg',
      //   'https://fastly.jsdelivr.net/npm/@vant/assets/apple-4.jpeg'
      // ],
      imgList: ['/assets/main/img1.png', '~@/assets/main/img2.png'],
      MyCourse: [
        {
          teachers: '',
          startTime: '',
          endTime: '',
        },
        {},
      ],
      loadingShow: true,
    };
  },
  components: {
    Swiper,
    SwiperItme,
    TitleBar,
    Curriculum,
    Surface,
    TabBar,
  },
  beforeRouteEnter(to, from, next) {
    console.log('-------beforeRouteEnter-------------');
    let _platform = window.sessionStorage.getItem('__platform__');
    console.log(`平台标识：${_platform === platform.android ? '安卓' : _platform === platform.ios ? 'ios' : '未知设备'}==>${_platform}`);
    const params = JSON.stringify({
      useTitleBar: false,
      objName: nameSpace, // objName app 调用的web方法命名空间，用于隔离window全局方法
    });
    if (_platform + '' === platform.ios) {
      window.webkit.messageHandlers.client_isAlready.postMessage(params);
    } else if (_platform + '' === platform.android) {
      window.DDBESOFFICE.client_isAlready(params);
    }
    Bus.on('token_save', () => {
      next();
    });
  },
  created() {
    this.initData();
  },
  onload() {
    this.initData();
  },
  onpageshow() {
    this.initData();
  },
  mounted() {
    // Dialog.confirm({
    //   title: '课程信息已改变',
    //   message: '您所参加的“xxx”上课时间已发生改变',
    //   confirmButtonText: '前往查看',
    //   confirmButtonColor: '#326FFF'
    // })
    //   .then(() => {
    //     console.log('点击了去认定')
    //   })
    //   .catch(() => {
    //     console.log('点击了取消')
    //   })
  },
  methods: {
    initData() {
      // 获取我的课程
      getMyCourse()
        .then((res) => {
          this.MyCourse = [];
          this.loadingShow = false;
          res.forEach((item) => {
            let courseState = getCourseState(item.startTime, item.endTime, item.type, item.status);
            if (courseState === '已开课') {
              this.MyCourse.push(item);
            }
          });
          console.log('this.MyCourse', this.MyCourse);
        })
        .catch((err) => {
          this.loadingShow = false;
          this.MyCourse = [];
        });
    },
    lookAll() {
      this.$router.push('/remind');
    },
    changeLoading(bol) {
      this.loadingShow = bol;
    },
  },
};
</script>

<style lang="scss" scoped>
.panel {
  overflow: hidden;
  padding: 20px;
  height: 1300px;
  background-color: rgb(245, 246, 247);
  .swiper {
    width: 698px;
    .s-img {
      width: 698px;
      height: 268px;
    }
  }
  .swiper2 {
    width: 100%;
  }

  .title-bar {
    margin: 20px 0;
    margin-top: 30px;
    img {
      width: 18px;
    }
  }
}
</style>
