<template>
  <div class="box">
    <div class="study-time">
      <div>
        本周学习<span class="data">{{ totalStudyTime }}</span
        >分钟
      </div>
      <div class="st-date">
        <span
          >超过<span class="data">{{ surpass }}</span
          >的人</span
        >
        <!-- <span class="date">
          <span>7月 第八周</span>
          <img src="@/assets/main/down.png" alt="" />
        </span> -->
        <van-dropdown-menu active-color="#1989fa">
          <van-dropdown-item v-model="value1" :options="option1" @change="changeOptionDate" />
        </van-dropdown-menu>
      </div>
    </div>
    <div id="surface"></div>
    <div class="show-data">
      <!-- <span>20min</span> -->
    </div>
    <div class="dian"></div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import * as echarts from 'echarts';

import { getStudyTime } from '@/api/course.js';

let colorArr = ['#a6b5ff', '#a6b5ff', '#9692ff', '#33cef2'];
let leftOffset = ref('8px');

const emit = defineEmits(['changeLoading']);

onMounted(() => {
  // 基于准备好的dom，初始化echarts实例
  if (document.getElementById('surface') == null) return;
  echarts.dispose(document.getElementById('surface'));
  myChart = echarts.init(document.getElementById('surface'));
});

let myOptions = ref({
  xAxis: {
    type: 'category',
    axisTick: {
      show: false,
    },
    axisLine: {
      lineStyle: {
        width: 0.2,
      },
    },
    data: ['一', '二', '三', '四', '五', '六', '日'],
  },
  yAxis: {
    show: false,
    type: 'value',
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
    },
    hideDelay: 100,
    // triggerOn: 'none'
  },
  grid: {
    top: '20px',
    left: '0px',
    right: '0px',
    bottom: '50px',
  },
  series: [
    {
      data: [0, 0, 0, 0, 0, 0, 0],
      type: 'line',
      smooth: true, // 平滑过度
      areaStyle: {
        opacity: 0.4,
        //关键在这里, 设置面积渐变
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          {
            offset: 0,
            color: colorArr[0], //这里是我设置的渐变的颜色从线条颜色变为白色，如果是循环数据，其中的0依次循环colorArr 就ok了
          },
          {
            offset: 1,
            color: '#fff',
          },
        ]),
      },
      lineStyle: {
        color: 'rgb(45, 107, 255)',
        width: 2,
      },
      symbol:
        'image://data:image/png;base64,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',
      symbolSize: 16,
    },
  ],
});

let myChart;

// 改变echart数据
const changeData = (myChart, option, data = [1, 2, 5, 6, 20, 32, 11]) => {
  option.series[0].data = data;
  myChart.setOption(option);
};

let date = new Date(); // 获取一个时间对象
// 获取今年是那一年
const year = date.getFullYear();

// 表格下拉菜单
const value1 = ref(1);
const option1 = [
  { text: `${year}年 第1周`, value: 1 },
  { text: `${year}年 第2周`, value: 2 },
];

// 获取一年中第几天对应第几周
var pad = function (year) {
  let weekOfYear = {};
  var d = new Date(year, 0, 1);
  let start = 0;
  while (d.getDay() != 1) {
    d.setDate(d.getDate() + 1);
  }
  start = d.getDate();
  let sumDay = 0; // 今年有多少天
  let days = 0;
  for (var i = 1; i < 13; i++) {
    days = new Date(year, i, 0); //获取某一个月的天数
    sumDay += days.getDate();
  }
  let weekNum = 2;
  let xingqi = 1;
  for (let i = 1; i < sumDay + 1; i++) {
    if (i < start) {
      weekOfYear[i] = 1;
    } else {
      weekOfYear[i] = weekNum;
      xingqi++;
      if (xingqi > 7) {
        xingqi = 1;
        weekNum++;
        option1.push({ text: `${year} 第${weekNum}周`, value: weekNum });
      }
    }
  }
  return weekOfYear;
};
let midCurrentYearWeek = pad(year);
//获取今天是第几天
let currentDay = Math.ceil((new Date() - new Date(new Date().getFullYear().toString())) / (24 * 60 * 60 * 1000));
value1.value = midCurrentYearWeek[currentDay];

// 获取学习记录, 绘制表格
let totalStudyTime = ref(''); // 总时长
let surpass = ref(''); // 百分比
let studyTimeData = ref([0, 0, 0, 0, 0, 0, 0]); // 这周的学习时长

let data = {
  weekOfYear: midCurrentYearWeek[currentDay],
  year: year,
};

const initStudyTime = (data) => {
  getStudyTime(data).then((res) => {
    emit('changeLoading', false);
    totalStudyTime.value = res.totalStudyTime;
    surpass.value = res.surpass;
    studyTimeData.value = [0, 0, 0, 0, 0, 0, 0];
    res.studyTimeRecordList.forEach((item) => {
      switch (item.dayOfWeek) {
        case 1:
          studyTimeData.value[0] = item.studyTime;
          break;
        case 2:
          studyTimeData.value[1] = item.studyTime;
          break;
        case 3:
          studyTimeData.value[2] = item.studyTime;
          break;
        case 4:
          studyTimeData.value[3] = item.studyTime;
          break;
        case 5:
          studyTimeData.value[4] = item.studyTime;
          break;
        case 6:
          studyTimeData.value[5] = item.studyTime;
          break;
        case 7:
          studyTimeData.value[6] = item.studyTime;
          break;
      }
    });
    // 绘制图表
    changeData(myChart, myOptions.value, studyTimeData.value);
  });
};
initStudyTime(data);

const changeOptionDate = (value) => {
  data.weekOfYear = value;
  initStudyTime(data);
  emit('changeLoading', true);
};
</script>

<style>
.van-dropdown-menu__bar {
  height: 40px;
}
.van-dropdown-menu__title {
  background: rgb(245, 246, 247);
  border: 0;
}
</style>

<style lang="scss" scoped>
.box {
  position: relative;
  #surface {
    border-radius: 30px;
    width: 700px;
    height: 380px;
    background-color: #fff;
  }
  .study-time {
    margin-left: 34px;
    margin-bottom: 8px;
    font-size: 24px;
    font-family:
      PingFangSC-Regular,
      PingFang SC;
    font-weight: 400;
    color: #444956;
    .data {
      font-size: 36px;
      color: #000;
    }
    .st-date {
      margin-right: 24px;
      display: flex;
      justify-content: space-between;
      .date {
        font-size: 26px;
        font-family:
          PingFangSC-Regular,
          PingFang SC;
        font-weight: 400;
        color: #878b95;
        img {
          width: 19px;
          margin-left: 7px;
        }
      }
    }
  }
  .show-data {
    position: absolute;
    bottom: 0;
    // left: 20px;
    left: v-bind(leftOffset);
    height: 60px;
    width: 100%;
    // background-color: rgba(0, 0, 0, 0.2);
    span {
      font-size: 24px;
      font-family: DINAlternate-Bold, DINAlternate;
      font-weight: bold;
      color: #878b95;
    }
  }
  .dian {
    position: absolute;
  }
}
</style>
