<script setup>
import { ref } from 'vue';
import { onBeforeRouteLeave, useRoute, useRouter } from 'vue-router';
import { HomeApi } from '@/api';
import { backClient } from '@/utils';
import moment, { locales } from 'moment';
import emptyImage from '@/assets/course/empty.png';

const router = useRouter();
const route = useRoute();

/** 页面初始化 **/

const page = ref(1);
const size = ref(0);
const total = ref(0);
const loading = ref(false);
const finished = ref(false);
const planList = ref([]);

const reqResult = ref(null);

async function getPlanList() {
  try {
    size.value += 10;
    const [year, month] = currentDate.value;
    const res = await reqResult.value(page.value, size.value, year, month);
    if (res.code === 0) {
      total.value = res.data.total;
      planList.value = res.data.planVoList || res.data.records;
      console.log(res.data);
    } else {
      console.log(res.msg || res.message);
    }
  } catch (e) {
    console.log('请求计划列表失败，请稍后重试', e);
  } finally {
    loading.value = false;
    finished.value = total.value <= planList.value.length;
  }
}

/* tabs */

let storageTabActive = sessionStorage.getItem('courseTableTabActive');
storageTabActive = storageTabActive ? Number(storageTabActive) : 0;

changeReqApi(storageTabActive);

const tabActive = ref(storageTabActive);

function changeReqApi(key) {
  switch (key) {
    case 0:
      reqResult.value = HomeApi.getPlanList;
      break;
    case 1:
      reqResult.value = HomeApi.getFinishedPlanList;
      break;
    case 2:
      reqResult.value = HomeApi.getEndedPlanList;
      break;
  }
}

function handleTabChange(key) {
  changeReqApi(key);
  size.value = 0;
  getPlanList();
}

onBeforeRouteLeave((to, from, next) => {
  if (to.name === 'Details') {
    sessionStorage.setItem('courseTableTabActive', tabActive.value);
  } else {
    sessionStorage.removeItem('courseTableTabActive');
  }
  next();
});

/* 筛选 */

const showFilter = ref(false);
const currentDate = ref([new Date().getFullYear(), new Date().getMonth() + 1]);
const lastDate = ref([new Date().getFullYear(), new Date().getMonth() + 1]);

function handleFilterConfirm() {
  lastDate.value = currentDate.value;
  size.value = 0;
  getPlanList();
  showFilter.value = false;
}

function handleFilterCancel() {
  currentDate.value = lastDate.value;
  showFilter.value = false;
}

/** 折叠面板 **/

const activeNames = ref('');
const courseList = ref({});

async function changePanel(val) {
  if (!val) return;
  try {
    console.log(val, typeof val);
    const res = await HomeApi.getCourseListByPlanId(val);
    if (res.code === 0) {
      console.log(res.data);
      courseList.value[val] = res.data;
    } else {
      console.log(res.msg || res.message);
    }
  } catch (e) {
    console.log('请求计划列表失败，请稍后重试', e);
  }
}

// 进入课程详情
function handleCardClick(courseId, planId, isFinish) {
  if (tabActive.value === 0) {
    router.push(`/details/${courseId}/${planId}`);
  } else {
    router.push(`/details/${courseId}/${planId}?isFinish=${isFinish}`);
  }
}

// 获取计划剩余天数
function getPlanLeftDays(endTimeStr) {
  const currentDate = moment();
  const endDate = moment(endTimeStr, 'YYYY-MM-DD HH:mm:ss');
  return endDate.diff(currentDate, 'days');
}

/** 更多入口 */

const showPopover = ref(false);

const actions = [{ text: '错题本', icon: 'description' }];

function handleSelect(_, index) {
  switch (index) {
    case 0:
      // 跳转到错题本
      router.push(`/course-wrong-table`);
      break;
  }
}
</script>

<template>
  <div class="flex flex-col overflow-hidden">
    <van-nav-bar title="培训系统" left-arrow @click-left="backClient" left-text="返回">
      <template #right>
        <van-icon v-if="tabActive === 2" name="filter-o" size="20" @click="showFilter = true" />
        <van-popover v-else placement="bottom-end" v-model:show="showPopover" :actions="actions" @select="handleSelect">
          <template #reference>
            <van-icon name="ellipsis" size="25" />
          </template>
        </van-popover>
      </template>
    </van-nav-bar>
    <van-tabs v-model:active="tabActive" @change="handleTabChange" title-active-color="#326FFF" color="#326FFF">
      <van-tab title="进行中"></van-tab>
      <van-tab title="已完成"></van-tab>
      <van-tab title="已结束"></van-tab>
    </van-tabs>
    <div class="flex-1 overflow-auto">
      <van-list v-model:loading="loading" :finished="finished" :finished-text="planList.length > 0 ? '没有更多了' : ''" @load="getPlanList">
        <van-collapse v-if="total > 0" v-model="activeNames" @change="changePanel" accordion>
          <div v-for="plan in planList" :key="plan.id">
            <van-collapse-item
              :title="plan.name"
              :label="tabActive === 0 ? '还剩' + getPlanLeftDays(plan.endTime) + '天' : plan.startTime + ' ~ ' + plan.endTime"
              :name="plan.id"
            >
              <!--  列表  -->
              <template v-for="item in courseList[plan.id]" :key="item.id">
                <div class="relative flex mb-[32px]" @click="handleCardClick(item.id, plan.id, item.isFinish)">
                  <img class="w-[240px] h-[176px] mr-[32px] rounded-[10px]" :src="item.thumb" alt="" />
                  <div class="flex-1 flex flex-col">
                    <div class="h-[95px] text-[30px] font-font font-normal text-titleColor my-text-two-lines-overflow">
                      {{ item.title }}
                    </div>
                    <div class="flex items-center mb-[12px] w-[300px]">
                      <img src="@/assets/comps/course-panel/category.png" class="w-[28px] h-[28px] mr-[16px]" alt="" />
                      <span class="text-[26px] font-font font-normal text-descColor my-text-one-line-overflow">{{
                        item.categoryVo.name || '未分类'
                      }}</span>
                    </div>
                    <div class="flex items-center w-[250px]">
                      <img src="@/assets/comps/course-panel/user.png" class="w-[28px] h-[28px] mr-[16px]" alt="" />
                      <span class="text-[26px] font-font font-normal text-descColor my-text-one-line-overflow">讲师 {{ item.teacher }}</span>
                    </div>
                  </div>
                  <div class="absolute right-0 bottom-0 text-[26px] font-font font-normal text-descColor">
                    {{ item.userCount }}
                    人参加
                  </div>
                </div>
              </template>
            </van-collapse-item>
          </div>
        </van-collapse>
        <div v-else class="mt-[200px]">
          <van-empty :image="emptyImage" image-size="120" description="暂无课程信息" />
        </div>
      </van-list>
    </div>

    <!-- 时间筛选 -->
    <van-popup v-model:show="showFilter" round position="bottom" :style="{ height: '50%' }">
      <van-date-picker
        v-model="currentDate"
        title="选择年月"
        :min-date="new Date(2022, 0, 1)"
        :max-date="new Date(2050, 0, 1)"
        :columns-type="['year', 'month']"
        @cancel="handleFilterCancel"
        @confirm="handleFilterConfirm"
      />
    </van-popup>
  </div>
</template>

<style scoped lang="scss"></style>
