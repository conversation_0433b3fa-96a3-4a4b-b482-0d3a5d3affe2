<script setup>
import { TestApi } from '@/api';
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import QuestionAndAnalysis from '@/components/test/QuestionAndAnalysis.vue';
import StepQuestion from '@/components/test/StepQuestion.vue';

const router = useRouter();

/**
 * 题目列表
 */

const index = ref(0);
const list = ref([]);
const total = ref(0);

function getQuestionList() {
  TestApi.getWrongQuestionBank(1, 10000)
    .then((res) => {
      if (res.code === 0) {
        total.value = res.data.total;
        list.value = res.data.wrongRecords.map((item) => {
          item.question.mode = 'write';
          item.question.wrongQuestionId = item.id;
          return item.question;
        });
      } else {
        console.log(res.msg);
      }
    })
    .catch((e) => {
      console.log('请求题目列表失败，请稍后重试', e);
    });
}

/**
 * 导航
 */

const showPopup = ref(false);

function handleChangeToNext() {
  if (index.value < total.value - 1) {
    index.value++;
    multiAnswer.value = null;
  }
}

function handleChangeToLast() {
  if (index.value > 0) {
    index.value--;
    multiAnswer.value = null;
  }
}

function handleShowPopup() {
  showPopup.value = true;
}

function handleCardClick(i) {
  index.value = i;
  showPopup.value = false;
  multiAnswer.value = null;
}

/**
 * 提交
 */

const multiAnswer = ref(null);

function handleChangeAnswer(questionId, question, answerStr) {
  const { type, wrongQuestionId } = question;
  const data = { wrongQuestionId, questionId, type, answerStr };
  if (question.type === 'SINGLE' || question.type === 'T_OR_F') {
    submitQuestion(data);
  } else if (question.type === 'MULTI') {
    multiAnswer.value = answerStr ? { ...data } : null;
  }
}

function handleSubmitMultiAnswer() {
  if (multiAnswer.value) {
    submitQuestion(multiAnswer.value);
    multiAnswer.value = null;
  }
}

function submitQuestion({ wrongQuestionId, questionId, type, answerStr }) {
  TestApi.submitWrongQuestion(wrongQuestionId, {
    type,
    questionId,
    answers: answerStr.split(','),
  })
    .then((res) => {
      if (res.code === 0) {
        const { isRight, wrongAnswers } = res.data;
        const params = {
          mode: 'read',
          isRight,
          myAnswer: wrongAnswers.join(','),
        };
        list.value[index.value] = { ...list.value[index.value], ...params };
      } else {
        console.log(res.msg);
      }
    })
    .catch((e) => {
      console.log('提交失败，请稍后重试', e);
    });
}

/**
 * 初始化
 */

(async () => {
  await getQuestionList();
})();
</script>

<template>
  <div class="h-full flex flex-col bg-grey overflow-hidden">
    <!-- 标题栏 -->
    <van-nav-bar :title="total > 0 ? `${index + 1} / ${total}` : '错题本'" left-text="返回" left-arrow @click-left="() => router.back()">
    </van-nav-bar>
    <!-- 题目选项及答案解析 -->
    <div v-if="list[index]" class="flex-1 overflow-scroll">
      <question-and-analysis
        :key="index"
        :mode="list[index].mode"
        :index="index"
        :question="list[index]"
        @change="handleChangeAnswer"
        :isRight="list[index].isRight"
        :myAnswer="list[index].myAnswer"
      />
    </div>
    <div v-else class="flex-1">
      <van-empty class="mt-[50px]" description="暂无错题" />
    </div>
    <!-- d多选题目确定按钮-->
    <div v-if="list[index] && list[index].type === 'MULTI' && list[index].mode === 'write'">
      <div @click="handleSubmitMultiAnswer" class="mx-auto mb-[40px] w-[580px] h-[100px] bg-blue rounded-[15px] text-center leading-[100px]">
        <span class="text-[34px] font-font font-normal text-white">确定</span>
      </div>
    </div>
    <!--  导航  -->
    <step-question
      v-if="total > 0"
      :index="index"
      :length="total"
      @handle-change-to-next="handleChangeToNext"
      @handle-change-to-last="handleChangeToLast"
      @show-popup="handleShowPopup"
    />
    <!-- 答题卡 -->
    <van-popup :show="showPopup" round position="bottom" class="flex flex-col h-3/5" @click-overlay="showPopup = false">
      <div class="my-[40px] text-center">
        <span class="text-[32px] font-font font-bold text-titleColor">答题卡</span>
      </div>
      <div class="flex-1 px-[10px] overflow-auto">
        <div class="grid grid-cols-6 gap-y-[24px] justify-items-center">
          <div v-for="(item, key) in list" :key="key" @click="handleCardClick(key)">
            <div
              class="w-[76px] h-[76px] rounded-full bg-[#F5F6FA] border text-center leading-[76px]"
              :class="{
                'bg-bgBlue text-blue border-blue': key === index,
                'bg-blue text-white border-blue': item.mode === 'read' && item.isRight,
                'bg-orange text-white': item.mode === 'read' && !item.isRight,
              }"
            >
              <span>{{ key + 1 }}</span>
            </div>
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>
