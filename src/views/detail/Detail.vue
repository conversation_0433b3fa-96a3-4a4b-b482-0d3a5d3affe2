<template>
  <NavBar :title="'课程详情'"> </NavBar>
  <div class="panel">
    <div class="detail" ref="detailRef">
      <Introduce :introduce-data="introduceData"></Introduce>
      <OnlineCourse ref="onLineCourseRef" :course-info="courseInfo" @handle-select-bar="handleSelectBar" v-if="Type === '1'"></OnlineCourse>
      <OfflineCourse :course-info="courseInfo" v-else></OfflineCourse>
      <Catalogue :catalogue="catalogue" :teachers="teachers" v-if="Type === '1'"></Catalogue>
    </div>
  </div>
  <Loading :isShow="isShowLoading"></Loading>
</template>

<script setup>
import { provide, ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { useCourseStore } from '@/store';

import Introduce from './childComps/Introduce.vue';
import OfflineCourse from './childComps/OfflineCourse.vue';
import OnlineCourse from './childComps/OnlineCourse.vue';
import Catalogue from './childComps/Catalogue.vue';

import { getCourseDetail, getUrl } from '@/api/course.js';
import { getCourseState } from '@/utils/index.js';

const route = useRoute();
const courseStore = useCourseStore();
// 课程相关信息
let introduceData = ref({});
let courseInfo = ref({});
let catalogue = ref([]);
let teachers = ref('');
let Type = 0; // 线上还是线下
let courseState = ref();
let IntimeId = ref();
let isShowLoading = ref(true);
let detailRef = ref();
let onLineCourseRef = ref();

provide('courseState', courseState);
provide('timeId', IntimeId);

const initData = () => {
  const { courseId, type, timeId } = route.query;
  Type = type + '';
  getCourseDetail(courseId, type, timeId)
    .then((res) => {
      console.log('课程详情', res);
      isShowLoading.value = false;
      IntimeId.value = res.timeId;
      introduceData.value = {
        title: res.content,
        tearcher: res.teachers,
        endTime: res.endTime,
        startTime: res.startTime,
        imageUrl: res.imageUrl,
        isTemporary: res.isTemporary,
      };
      courseInfo.value = {
        description: res.description,
        type,
        state: getCourseState(res.startTime, res.endTime, res.type, res.status),
        place: res.place,
      };
      catalogue.value = res.courseDataVoList;
      teachers.value = res.teachers;
      courseState.value = getCourseState(res.startTime, res.endTime, res.type, res.status);

      courseStore.setCurrentCourseList(res.courseDataVoList);
      courseStore.currentCourseState = getCourseState(res.startTime, res.endTime, res.type, res.status);
      courseStore.currentCourseTimeId = res.timeId;
    })
    .catch((err) => {
      isShowLoading.value = false;
    });
};

const handleSelectBar = (index) => {
  if (index === 0) {
    detailRef.value.scrollTop = 0;
  } else if (index === 1) {
    detailRef.value.scrollTop = 290;
  }
};

initData();
</script>

<style lang="scss" scoped>
.detail {
  overflow: auto;
  height: calc(100vh - 180px);
  width: 100%;
  background-color: rgb(245, 246, 247);
}
</style>
