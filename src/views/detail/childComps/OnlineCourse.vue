<template>
  <SelectBar :index="0" @handle-select-bar="handleSelectBar"></SelectBar>
  <CourseInfo :courseMsg="courseMsg" :course-info="courseInfo"></CourseInfo>
</template>

<script setup>
import { ref } from 'vue';
import CourseInfo from './CourseInfo.vue';
import SelectBar from './SelectBar.vue';
const courseMsg = [
  {
    img: '/src/assets/detail/mode.png',
    title: '线上授课',
  },
];
defineProps({
  courseInfo: Object,
});
let emit = defineEmits(['handleSelectBar']);

const handleSelectBar = (index) => {
  emit('handleSelectBar', index);
};
</script>

<style lang="scss" scoped></style>
