<template>
  <div class="catalogue">
    <div class="title">课程目录</div>
    <div class="section-list">
      <div class="section" v-for="(item, index) in catalogue" :key="index">
        <div class="right">
          <div class="sec-name" @click="goLook(item.appFileVoList[0], item.chapter, item.courseId)">{{ item.appFileVoList[0].name }}</div>
          <div class="sec-duration">
            <span>时长：{{ format(item.appFileVoList[0].duration) }}分钟</span>
          </div>
        </div>
        <div class="left">
          <img
            src="@/assets/detail/playVideo.png"
            v-if="courseState !== '未开课'"
            @click="goLook(item.appFileVoList[0], item.chapter, item.courseId)"
            alt=""
          />
          <img src="@/assets/detail/lock.png" v-else alt="" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { inject, ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';

import { platform } from '@/config/index.js';
import { getUrl, upRecord, getRecord } from '@/api/course.js';
import { useBaseStore } from '../../../store';

const router = useRouter();

const courseState = inject('courseState');
const timeId = inject('timeId');
const props = defineProps({
  catalogue: Array,
  teachers: String,
});

const baseStore = useBaseStore();

// 点击了播放
const goLook = (fileItem, chapter, courseId) => {
  if (courseState.value === '未开课') return;
  let title = fileItem.name.split('.')[0];

  router.push({
    path: '/video',
    query: {
      id: fileItem.id,
      title,
      chapter,
      courseId,
      timeId: timeId.value,
      teachers: props.teachers,
      duration: (fileItem.duration / 1000 / 60).toFixed(0),
    },
  });
};
function format(duration) {
  // var minutes = parseInt((duration % (1000 * 60 * 60)) / (1000 * 60));
  let min = Math.floor(duration / (1000 * 60));

  return min;
}
</script>

<style lang="scss" scoped>
.catalogue {
  box-sizing: border-box;
  margin-top: 16px;
  width: 100%;
  background-color: #fff;
  padding-left: 42px;
  padding-top: 24px;
  .title {
    font-size: 30px;
    font-family:
      PingFangSC-Medium,
      PingFang SC;
    font-weight: 600;
    color: #23252a;
    margin-bottom: 26px;
  }
  .section-list {
    padding-bottom: 20px;
    .section {
      box-sizing: border-box;
      width: 666px;
      height: 121px;
      background: #f5f6f7;
      border-radius: 10px;
      padding-left: 32px;
      padding-top: 18px;
      display: flex;
      justify-content: space-between;
      border-bottom: 1px solid transparent;
      .right {
        .sec-name {
          font-size: 28px;
          width: 570px;
          font-family:
            PingFangSC-Regular,
            PingFang SC;
          font-weight: 400;
          color: #444956;
          margin-bottom: 11px;
          line-height: 33px;
          letter-spacing: 2px;
          //设置宽度，超过时用...表示
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }
        .sec-duration {
          font-size: 24px;
          font-family:
            PingFangSC-Regular,
            PingFang SC;
          font-weight: 400;
          color: #878b95;
          line-height: 33px;
        }
      }
      .left {
        line-height: 100px;
        margin-right: 27px;
        img {
          width: 34px;
        }
      }
    }
    .section + .section {
      margin-top: 16px;
    }
  }
}
</style>
