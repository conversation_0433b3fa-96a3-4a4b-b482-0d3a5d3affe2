<template>
  <div class="introduce">
    <div class="img">
      <img :src="introduceData.imageUrl" alt="" />
    </div>
    <div class="title">
      <span>{{ introduceData.title }}</span>
      <span class="state before-start" v-if="courseState === '未开课'">未开课</span>
      <span class="state" v-else-if="courseState === '已开课'">已开课</span>
      <span class="state finished" v-else-if="courseState === '已完成'">已完成</span>
      <span class="state end" v-else-if="courseState === '已结束'">已结束</span>
      <span class="state xia" v-else>线下</span>
    </div>
    <div class="in-line one">
      <img src="@/assets/base/person.png" alt="" />
      <span>讲师 {{ introduceData.tearcher }}</span>
    </div>
    <div v-if="!introduceData.isTemporary" class="in-line">
      <img src="@/assets/base/time.png" alt="" />
      <span>{{ parseTimeToDate(introduceData.startTime) }}- {{ parseTimeToDate(introduceData.endTime) }}</span>
    </div>
    <div v-else class="in-line">
      <img src="@/assets/base/time.png" alt="" />
      <span>{{ parseTimeTomin(introduceData.startTime) }}</span>
    </div>
  </div>
</template>

<script setup>
import { inject, onMounted, ref } from 'vue';
import { parseTimeToDate, parseTimeTomin } from '@/utils/index.js';

const props = defineProps({
  introduceData: Object,
});
const courseState = inject('courseState');
console.log('课程状态是什么', courseState.value);
</script>

<style lang="scss" scoped>
.introduce {
  margin-bottom: 16px;
  padding: 0 42px;
  padding-bottom: 24px;
  background-color: #fff;
  .img {
    width: 665px;
    height: 325px;
    // background-color: rgb(113, 113, 113);
    overflow: hidden;
    img {
      width: 100%;
      border-radius: 16px;
    }
  }
  .title {
    margin-top: 32px;
    margin-bottom: 16px;
    font-size: 40px;
    font-family:
      PingFangSC-Semibold,
      PingFang SC;
    font-weight: 600;
    color: #23252a;

    .state {
      margin-left: 30px;
      padding: 10px;
      background: rgba(50, 111, 255, 0.1);
      border-radius: 8px;
      font-size: 22px;
      font-family:
        PingFangSC-Regular,
        PingFang SC;
      font-weight: 400;
      color: #326fff;
    }
    .before-start,
    .xia {
      background: #fcf3e3;
      color: #e9a12a;
    }
    .end {
      color: #f2641a;
      background: rgba(242, 100, 26, 0.1);
    }
    .finished {
      color: #16c125;
      background: rgba(43, 229, 60, 0.1);
    }
  }
  .in-line {
    span {
      font-size: 28px;
      font-family:
        PingFangSC-Regular,
        PingFang SC;
      font-weight: 400;
      color: #444956;
    }
    img {
      margin-right: 17px;
      width: 24px;
      vertical-align: middle;
    }
  }
  .one {
    margin-bottom: 20px;
  }
}
</style>
