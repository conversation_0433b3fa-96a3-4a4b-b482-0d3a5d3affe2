<template>
  <div class="nav-bar">
    <div class="options">
      <span class="option" :class="{ activeOption: currentOption === i }" v-for="(item, i) in options" :key="i" @click="handleOption(i)">{{
        item
      }}</span>
    </div>

    <img :class="{ moveLine: currentOption === 1 }" class="blueLine" src="@/assets/detail/blueLine.png" />
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

const route = useRoute();

const options = ['简介', '目录'];

const props = defineProps({
  index: {
    type: Number,
    default: 0,
  },
});

const emit = defineEmits(['handleSelectBar']);
let currentOption = ref(props.index);

const handleOption = (index) => {
  currentOption.value = index;

  if (index === 0) {
    emit('handleSelectBar', index);
  } else if (index === 1) {
    emit('handleSelectBar', index);
  }
};
</script>

<style lang="scss" scoped>
.nav-bar {
  box-sizing: border-box;
  position: relative;
  background-color: #fff;
  // width: 99%;
  padding-top: 22px;
  padding-left: 65px;
  .option {
    margin-right: 80px;
    font-size: 28px;
    font-family:
      PingFangSC-Regular,
      PingFang SC;
    font-weight: 400;
    color: #878b95;
    line-height: 40px;
  }
  .activeOption {
    font-size: 28px;
    font-family:
      PingFangSC-Medium,
      PingFang SC;
    font-weight: 500;
    color: #23252a;
  }
  .blueLine {
    display: inline-block;
    position: absolute;
    left: 65px;
    // left: 204px;
    width: 50px;
  }
  .moveLine {
    left: 204px;
  }
}
</style>
