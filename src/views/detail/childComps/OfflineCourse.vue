<template>
  <CourseInfo :courseMsg="courseMsg" :course-info="courseInfo"></CourseInfo>
</template>

<script setup>
import { ref } from 'vue';
import CourseInfo from './CourseInfo.vue';

const props = defineProps({
  courseInfo: Object,
});
const courseMsg = ref([
  {
    img: '/src/assets/detail/mode.png',
    title: '线下授课',
  },
  {
    img: '/src/assets/detail/place.png',
    title: '',
  },
]);
</script>

<style lang="scss" scoped></style>
