<template>
  <div class="info">
    <div class="title">课程信息</div>
    <p class="desc">{{ courseInfo.description }}</p>
    <div class="line"></div>
    <div class="i-item">
      <img :src="mode" alt="" />
      <span>{{ courseMsg[0].title }}</span>
    </div>
    <div class="i-item" v-if="courseMsg[1]">
      <img :src="place" alt="" />
      <span>{{ courseInfo.place }}</span>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import mode from '@/assets/detail/mode.png';
import place from '@/assets/detail/place.png';
const props = defineProps({
  courseMsg: Array,
  courseInfo: Object,
});
</script>

<style lang="scss" scoped>
.info {
  background-color: #fff;
  padding: 24px 24px 24px 42px;
  .title {
    font-size: 30px;
    font-family:
      PingFangSC-Medium,
      PingFang SC;
    font-weight: 600;
    color: #1b1c1e;
    margin-bottom: 13px;
  }
  .desc {
    width: 660px;
    font-size: 28px;
    font-family:
      PingFangSC-Regular,
      PingFang SC;
    font-weight: 400;
    color: #444956;
    line-height: 48px;
    letter-spacing: 2px;
    word-break: break-all;
  }
  .line {
    margin: 20px 0;
    width: 666px;
    height: 2px;
    background-color: #e4e9ef;
  }
  .i-item {
    margin-bottom: 20px;
    img {
      width: 25px;
      margin-right: 10px;
    }
    span {
      font-size: 28px;
      font-family:
        PingFangSC-Regular,
        PingFang SC;
      font-weight: 400;
      color: #444956;
    }
  }
  :last-child {
    margin-bottom: 0px;
  }
}
</style>
