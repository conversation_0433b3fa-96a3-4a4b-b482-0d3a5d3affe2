<template>
  <!-- <NavBar :title="'培训系统'"></NavBar> -->
  <Search @handleSearch="handleSearch"></Search>
  <div class="panel" ref="panelRef" @touchend="touchend">
    <div class="title-bar" v-show="undoneCourse.length > 0">
      <TitleBar :title="'未完成'" :messageNum="undoneCourse.length"></TitleBar>
    </div>

    <template v-for="item in undoneCourse" :key="item.id">
      <CourseItem :course="item" :status="0"></CourseItem>
    </template>

    <div class="title-bar" v-show="doneCourse.length > 0">
      <TitleBar :title="'已完成'" :messageNum="doneCourse.length"></TitleBar>
    </div>
    <template v-for="item in doneCourse" :key="item.id">
      <CourseItem :course="item"></CourseItem>
    </template>

    <div class="blank" v-show="doneCourse.length === 0 && undoneCourse.length === 0">
      <img src="@/assets/course/blank.png" alt="" />
    </div>
    <div class="lan"></div>

    <TabBar></TabBar>
  </div>
  <Loading :isShow="loadingShow"></Loading>
</template>

<script setup>
import { onMounted, ref, onActivated, nextTick } from 'vue';
import { useRoute } from 'vue-router';
import Search from './childComps/Search.vue';
import TitleBar from '../../components/TitleBar.vue';
import CourseItem from './childComps/CourseItem.vue';
import TabBar from '@/components/TabBar.vue';

import list1 from '@/assets/img/course/list1.png';
import list2 from '@/assets/img/course/list2.png';
import list3 from '@/assets/img/course/list3.png';

import { getCourseState } from '@/utils';
import { useCourseStore } from '@/store';
import { getMyCourse, searchCourse } from '@/api/course.js';

const courseStore = useCourseStore();
const route = useRoute();

onMounted(() => {
  initData();
});

onActivated(async () => {
  console.log('activated---我来了', route);

  if (route.meta.isMain) {
    try {
      await initData();
      panelRef.value.scrollTop = courseStore.courseScrollTop;
    } catch (err) {
      console.log(err);
    }
  } else {
    panelRef.value.scrollTop = courseStore.courseScrollTop;
  }
});

let loadingShow = ref(true);
let imgs = [list1, list2, list3];
const doneCourse = ref([]);
const undoneCourse = ref([]);

const Course = ref([{}, {}]);
const initData = () => {
  console.log('请求了课表数据');
  getMyCourse()
    .then((res) => {
      loadingShow.value = false;
      Course.value = res;

      undoneCourse.value = [];
      doneCourse.value = [];
      res.forEach((item) => {
        if (item.status === 0) {
          item.cover = imgs[Math.ceil(Math.random() * 3) - 1];
          let courseState = getCourseState(item.startTime, item.endTime, item.type, item.status);
          if (courseState === '已结束') {
            doneCourse.value.push(item);
          } else {
            undoneCourse.value.push(item);
          }
        } else if (item.status === 1) {
          doneCourse.value.push(item);
        }
      });
      console.log('doneCourse.value-----', doneCourse.value);

      // 根据时间排序doneCourse
      function compare(p) {
        //这是比较函数
        return function (m, n) {
          var a = m[p];
          var b = n[p];
          return b - a; //升序
        };
      }
      doneCourse.value.sort(compare('startTime'));
      console.log('zuihou的数据了', doneCourse.value);
    })
    .catch((err) => {
      loadingShow.value = false;
    });
};

// 搜索事件
const handleSearch = (val) => {
  console.log('搜索的是什么呢', val);
  searchCourse(val).then((res) => {
    console.log('搜索的结果是', res);
    undoneCourse.value = [];
    doneCourse.value = [];
    res.forEach((item) => {
      if (item.status === 0) {
        item.cover = imgs[Math.ceil(Math.random() * 3) - 1];
        undoneCourse.value.push(item);
      } else if (item.status === 1) {
        doneCourse.value.push(item);
      }
    });
  });
};

// 滑动事件
let panelRef = ref();
let panelScrollTop = 0;
const touchend = () => {
  panelScrollTop = parseInt(panelRef.value.scrollTop);
  courseStore.courseScrollTop = parseInt(panelRef.value.scrollTop);
};
</script>

<style lang="scss" scoped>
.panel {
  overflow: auto;
  padding: 0 32px;
  height: calc(100vh - 190px);
  background-color: rgb(245, 246, 247);
  .title-bar {
    margin: 20px 0;
    margin-left: 6px;
  }

  .blank {
    display: flex;
    justify-content: center;
    margin-top: 300px;
    img {
      width: 280px;
      height: 280px;
    }
  }
  .lan {
    width: 100%;
    height: 100px;
  }
}
</style>
