<template>
  <div class="main">
    <div class="nav-left">
      <img class="fl" src="@/assets/img/back.png" @click="back" />
    </div>
    <div class="search">
      <van-search v-model="value" @search="onSearch" placeholder="搜索课程名称" />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { appBack } from '@/utils/index.js';

const router = useRouter();
const route = useRoute();

defineProps({
  showTitle: {
    type: Boolean,
    default: true,
  },
});
const emit = defineEmits(['handleSearch']);
const value = ref('');
const onSearch = (val) => {
  console.log('搜索了');
  emit('handleSearch', val);
};

const back = () => {
  appBack(router, route);
};
</script>

<style lang="scss" scoped>
.main {
  width: 750px;
  height: 100px;
  background: #ffffff;
  position: relative;
  line-height: 100px;
  .nav-left {
    position: absolute;
    left: 30px;
    top: 6px;
    box-sizing: border-box;
    img {
      height: 30px;
      width: 30px;
    }
  }

  .search {
    margin-left: 50px;
  }
  :deep(.van-search__field) {
    height: 60px;
  }
}
</style>
