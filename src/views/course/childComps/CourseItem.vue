<template>
  <div class="course" @click="goCourseDetail(course)">
    <div class="left">
      <!-- <img src="@/assets/img/course/list1.png" alt="" /> -->
      <!-- <img :src="course.cover" alt="" /> -->
      <img :src="course.imageUrl" alt="" />
    </div>
    <div class="right">
      <div class="title">
        <span>{{ course.content }}</span>
        <span
          class="state"
          :class="{
            'after-start': courseState === '已开课',
            'before-start': courseState === '未开课',
            offline: courseState === '线下',
            closed: courseState === '已结束',
            finished: courseState === '已完成',
          }"
          >{{ courseState }}</span
        >
      </div>
      <div class="disc">
        <div class="lecturer">
          <img src="@/assets/base/person.png" alt="" />
          <span class="tea">讲师 {{ course.teachers }}</span>
        </div>
        <div v-if="!course.isTemporary">
          <img src="@/assets/base/time.png" alt="" />
          <span>
            {{ parseTimeToDate(course.startTime) }}-
            {{ parseTimeToDate(course.endTime) }}
          </span>
        </div>
        <div v-else>
          <img src="@/assets/base/time.png" alt="" />
          <span>
            {{ parseTimeTomin(course.startTime) }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, ref } from 'vue';

import { parseTimeToDate, getCourseState, parseTimeTomin } from '@/utils/index.js';
import { useRouter } from 'vue-router';

const props = defineProps({
  course: Object,
  status: Number,
});

// 当前课程状态
const courseState = ref('');

onMounted(() => {
  courseState.value = getCourseState(props.course.startTime, props.course.endTime, props.course.type, props.course.status);
  if (courseState.value === '未开课') {
    console.log(courseState.value);
  }
});

const router = useRouter();

const goCourseDetail = (course) => {
  router.push({
    path: '/detail',
    query: {
      courseId: course.id,
      type: course.type,
      img: course.cover,
      timeId: course.timeId,
    },
  });
};
</script>

<style lang="scss" scoped>
.course {
  width: 686px;
  height: 226px;
  background-color: #fff;
  border-radius: 30px;
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  .left {
    margin-left: 20px;
    width: 234px;
    height: 182px;
    border-radius: 20px;
    overflow: hidden;
    img {
      width: 100%;
    }
  }
  .right {
    position: relative;
    margin-left: 26px;
    height: 182px;
    display: flex;
    flex-direction: column;
    font-size: 24px;
    font-family:
      PingFangSC-Regular,
      PingFang SC;
    font-weight: 400;
    color: #444956;
    img {
      width: 24px;
      margin-right: 19px;
    }
    .title {
      font-size: 28px;
      font-family:
        PingFangSC-Semibold,
        PingFang SC;
      font-weight: 600;
      color: #23252a;
      // line-height: 50px;
      letter-spacing: 2px;

      span:nth-child(1) {
        display: inline-block;
        max-width: 240px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-left: 20px;
      }
      .state {
        margin-left: 20px;
        padding: 10px;
        background: rgba(50, 111, 255, 0.1);
        border-radius: 8px;
        font-size: 22px;
        font-family:
          PingFangSC-Regular,
          PingFang SC;
        font-weight: 400;
        color: #326fff;
      }
      .after-start {
        color: #326fff;
        background: rgba(50, 111, 255, 0.1);
      }
      .before-start {
        background: #fcf3e3;
        color: #e9a12a;
      }
      .offline {
        background: #fcf3e3;
        color: #e9a12a;
      }
      .finished {
        color: #16c125;
        background: rgba(43, 229, 60, 0.1);
      }
      .closed {
        color: #f2641a;
        background: rgba(242, 100, 26, 0.1);
      }
    }
    .disc {
      width: 300px;
      position: absolute;
      bottom: 0;
      .lecturer {
        margin-bottom: 10px;

        // .tea {
        //   width: 260px;
        // }
      }
    }
  }
}
</style>
