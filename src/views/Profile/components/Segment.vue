<script setup>
const props = defineProps({
  tabs: {
    type: Array,
    required: true,
  },
  modelValue: {
    type: String,
    required: true,
  },
  block: {
    type: Boolean,
    default: false,
  },
  size: {
    type: String,
    default: 'sm', // base | sm
  },
});

const emit = defineEmits(['update:modelValue']);

const selectTab = (tab) => {
  emit('update:modelValue', tab);
};
</script>

<template>
  <div class="flex bg-[#F0F1F1] rounded-full p-[8px]">
    <button
      v-for="tab in tabs"
      :key="tab"
      @click="selectTab(tab)"
      class="flex-1 py-[8px] rounded-full font-medium"
      :class="{
        'bg-white text-blue': modelValue === tab,
        'text-desc': modelValue !== tab,
      }"
    >
      {{ tab }}
    </button>
  </div>
</template>
