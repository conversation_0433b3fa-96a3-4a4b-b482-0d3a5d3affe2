<script setup>
import { useRouter } from 'vue-router'; 

const router = useRouter();

defineProps({
  baseInfo: {
    required: true,
    type: Object,
  },
  isCard: {
    type: Boolean,
    default: false,
  },
});
</script>

<template>
  <div @click="router.push('/profile/learning-statistics')" class="flex justify-between items-center py-[16px] relative rounded-[16px]" :class="{ 'bg-white shadow-sm py-[32px]': isCard, }">
    <div class="study-stats">
      <div>
        <span class="number">{{ baseInfo?.todayStudyTime }}</span>
        <span class="unit">分钟</span>
      </div>
      <div class="desc">今日学习</div>
    </div>
    <div class="study-stats">
      <div>
        <span class="number">{{ baseInfo?.continuousStudyDays }}</span>
        <span class="unit">天</span>
      </div>
      <div class="desc">连续学习</div>
    </div>
    <div class="study-stats">
      <div>
        <span class="number">{{ baseInfo?.studyDays }}</span>
        <span class="unit">天</span>
      </div>
      <div class="desc">学习天数</div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.study-stats {
  width: 200px;
  text-align: center;

  .number {
    font-size: 40px;
    font-weight: 500;
    line-height: 52px;
  }

  .unit {
    margin-left: 4px;
    font-size: 24px;
  }

  .desc {
    font-size: 24px;
    color: #878b95;
  }
}
</style>
