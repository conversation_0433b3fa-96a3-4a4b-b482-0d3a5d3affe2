<script setup>
import CourseCategory from '@/assets/profile/courseCategory.svg';
import CourseUser from '@/assets/profile/courseUser.svg';

defineProps({
  url: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    required: true,
  },
  category: {
    type: String,
    required: true,
  },
  teacher: {
    type: String,
    required: true,
  },
  coverTag: {
    type: String,
  },
});
</script>

<template>
  <div class="flex gap-2">
    <div class="relative w-[240px] h-[176px] rounded-[16px] overflow-hidden">
      <img :src="url" alt="课程封面" class="w-full h-full" />
      <div
        v-if="coverTag"
        class="absolute bottom-0 left-0 right-0 h-[50px] bg-gradient-to-t from-black/60 to-transparent flex items-center justify-end px-[8px]"
      >
        <span class="text-white text-[24px]">{{ coverTag }}</span>
      </div>
    </div>

    <div class="flex-1 h-[176px] flex flex-col gap-1">
      <div class="flex-1 text-[28px]">
        {{ title }}
      </div>
      <div class="flex items-center gap-1 text-desc text-[24px]">
        <img :src="CourseCategory" alt="部门 ICON" class="w-3 h-3" />
        <span>{{ category }}</span>
      </div>
      <div class="flex items-center gap-1 text-desc text-[24px]">
        <img :src="CourseUser" alt="部门 ICON" class="w-3 h-3" />
        <span class="flex-1">{{ teacher }}</span>
        <slot />
      </div>
    </div>
  </div>
</template>
