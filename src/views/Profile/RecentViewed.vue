<script setup>
import { useRouter } from 'vue-router';
import { computed, ref } from 'vue';
import CourseCard from './components/CourseCard.vue';
import Tabs from '../../components/Tabs.vue';
import { getDurationStr, getPlanTab, formatDuration } from './utils';
import { getRecentCourses } from '@/api/profile';
import { showToast } from 'vant';
import dayjs from 'dayjs';

const router = useRouter();

const isCourseLoading = ref(false);
const planTab = ref(0);
const searchValue = ref('');
const recentCourses = ref({
  inPlan: {
    today: [],
    yesterday: [],
    other: [],
  },
  outPlan: {
    today: [],
    yesterday: [],
    other: [],
  },
}); // 最近学习课程

const isEmpty = computed(() => {
  if (planTab.value === 0) {
    return Object.values(recentCourses.value.inPlan).every((item) => !item.length);
  }
  return Object.values(recentCourses.value.outPlan).every((item) => !item.length);
});

// 获取最近学习课程
async function getRecentCoursesList() {
  try {
    isCourseLoading.value = true;
    const [startTime, endTime] = getDurationStr('lastMonth');
    const res = await getRecentCourses(startTime, endTime);
    if (res.code !== 0) throw new Error(res.msg);
    recentCourses.value = res.data;
  } catch (error) {
    showToast(error.message);
  } finally {
    isCourseLoading.value = false;
  }
}

getRecentCoursesList();

function getLastStudyTime(lastStudyTime, isOther = false) {
  if (isOther) {
    return dayjs(lastStudyTime).format('M月D日');
  }
  return dayjs(lastStudyTime).format('HH:mm');
}
</script>

<template>
  <div class="flex flex-col overflow-hidden bg-white">
    <van-nav-bar title="最近观看" left-arrow left-text="返回" @click-left="router.back()" />
    <!-- <van-search v-model="searchValue" placeholder="搜索课程名称、讲师" /> -->
    <Tabs
      :show-border="true"
      :tabs="[
        {
          label: '计划内',
          value: 0,
        },
        {
          label: '计划外',
          value: 1,
        },
      ]"
      v-model="planTab"
    />

    <van-skeleton class="mt-[32px]" v-if="isCourseLoading" title :row="8" />

    <div v-if="!isCourseLoading && isEmpty" class="flex justify-center items-center h-[800px] text-[28px] text-desc">暂无数据</div>

    <div class="flex-1 overflow-auto">
      <div v-show="!isCourseLoading && planTab === 0">
        <div v-for="key in Object.keys(recentCourses.inPlan)" :key="key" class="px-[32px]">
          <template v-if="recentCourses.inPlan[key].length">
            <div class="mt-[16px] mb-[8px] text-[32px] font-medium">{{ getPlanTab(key) }}</div>
            <CourseCard
              v-for="item in recentCourses.inPlan[key]"
              class="mb-[16px]"
              :key="item.courseId"
              :url="item.thumb"
              :title="item.courseName"
              :category="item.categoryName"
              :teacher="item.teacher"
              :coverTag="
                item.isFinish
                  ? '已看完'
                  : `${formatDuration(item.currentHourFinishDuration || 0)}/${formatDuration(item.currentHourTotalDuration || 0)}`
              "
              @click="router.push(`/details/${item.courseId}/${item.planId}`)"
            >
              <template #default> {{ item.lastStudyTime ? getLastStudyTime(item.lastStudyTime, key === 'other') : '' }} </template>
            </CourseCard>
          </template>
        </div>
      </div>

      <div v-show="!isCourseLoading && planTab === 1">
        <div v-for="key in Object.keys(recentCourses.outPlan)" :key="key" class="px-[32px]">
          <template v-if="recentCourses.outPlan[key].length">
            <div class="mt-[16px] mb-[8px] text-[32px] font-medium">{{ getPlanTab(key) }}</div>
            <CourseCard
              v-for="item in recentCourses.outPlan[key]"
              class="mb-[16px]"
              :key="item.courseId"
              :url="item.thumb"
              :title="item.courseName"
              :category="item.categoryName"
              :teacher="item.teacher"
              :coverTag="
                item.isFinish
                  ? '已看完'
                  : `${formatDuration(item.currentHourFinishDuration || 0)}/${formatDuration(item.currentHourTotalDuration || 0)}`
              "
              @click="router.push(`/details/${item.courseId}`)"
            >
              <template #default> {{ item.lastStudyTime ? getLastStudyTime(item.lastStudyTime, key === 'other') : '' }} </template>
            </CourseCard>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>
