import dayjs from 'dayjs';

// 毫秒转换，不到一小时时转为 00:00，超过一小时时转为 01:00:00
export function formatDuration(duration) {
    const hours = Math.floor(duration / 3600000);
    const minutes = Math.floor((duration % 3600000) / 60000);
    const seconds = Math.floor((duration % 60000) / 1000);

    const padZero = (num) => num.toString().padStart(2, '0');

    if (hours > 0) {
        return `${padZero(hours)}:${padZero(minutes)}:${padZero(seconds)}`;
    }
    return `${padZero(minutes)}:${padZero(seconds)}`;
}

// 排名字体颜色
export const getRankFontColor = (num) => {
    if (num === 0) return '#EE4E35';
    if (num === 1) return '#EE8835';
    if (num === 2) return '#EBB00F';
    return 'rgba(29, 33, 41, 0.6)';
};

// 分钟转 x小时x分钟
export const formatStudyDuration = (minutes) => {
    const hours = Math.floor(minutes / 60);
    const _minutes = minutes % 60;
    if(hours === 0) {
        return `${_minutes}分钟`;
    }
    return `${hours}小时${_minutes}分钟`;
};

export const getDurationStr = (key) => {
    if (key === 'lastMonth') {
        return [dayjs().subtract(1, 'month').format('YYYY-MM-DD 00:00:00'), dayjs().format('YYYY-MM-DD 23:59:59')];
    }
    if (!['day', 'week', 'month', 'year'].includes(key)) {
        key = 'year';
    }
    return [dayjs().startOf(key).format('YYYY-MM-DD HH:mm:ss'), dayjs().endOf(key).format('YYYY-MM-DD HH:mm:ss')];
};

// 格式化时间
export const getPlanTab = (key) => {
    if (key === 'today') return '今天';
    if (key === 'yesterday') return '昨天';
    return '更早';
};

// 获取计划时间
export function getPlanTime(type, date) {
    if (!['month', 'year'].includes(type)) {
        type = 'year';
    }
    const startTime = dayjs(date).startOf(type).format('YYYY-MM-DD 00:00:00');
    const endTime = dayjs(date).endOf(type).format('YYYY-MM-DD 23:59:59');
    return { startTime, endTime };
}

export function clearNaN(val) {
    return isNaN(val) || val === "NaN" ? 0 : val;
}

// 获取一年的周数量
export function getWeeksOfYear(year) {
    const firstDate = new Date(`${year}-01-01`);
    const lastDate = new Date(`${year + 1}-01-01`);

    let date = firstDate;
    let month = firstDate.getMonth();
    let weekNumber = 1;
    const weeks = [];

    while (date <= lastDate) {
        weeks[month] = weeks[month] || [];
        ((weekNumber) => {
            weeks[month].push(weekNumber);
        })(weekNumber);
        date.setDate(date.getDate() + 7);
        if (date.getFullYear() === year + 1) break;
        weekNumber++;
        if (date.getMonth() !== month) {
            month = date.getMonth();
        }
    }

    return { weeks, weekNumber };
}

// 获取当前周是一年的第几周
export function getWeekOfYear(date) {
    const beginDate = new Date(date.getFullYear(), 0, 1);
    let endWeek = date.getDay();
    if (endWeek === 0) endWeek = 7;
    let beginWeek = beginDate.getDay();
    if (beginWeek === 0) beginWeek = 7;
    const millisDiff = date.getTime() - beginDate.getTime();
    const dayDiff = Math.floor((millisDiff + (beginWeek - endWeek) * (24 * 60 * 60 * 1000)) / 86400000);
    return Math.ceil(dayDiff / 7) + 1;
}

// 根据周月年确定 Select 选项
export function getOptionsFromStudyTimes(type) {
    switch (type) {
        case 1:
            return {
                title: '选择周',
                options: Array.from({ length: getWeeksOfYear(new Date().getFullYear()).weekNumber }, (_, index) => ({
                    text: `第${index + 1}周`,
                    value: index + 1,
                })),
                defaultValue: getWeekOfYear(new Date()),
                defaultText: `第${getWeekOfYear(new Date())}周`,
            };
        case 2:
            return {
                title: '选择月',
                options: Array.from({ length: 12 }, (_, index) => ({
                    text: `${index + 1}月`,
                    value: index + 1,
                })),
                defaultValue: new Date().getMonth() + 1,
                defaultText: `${new Date().getMonth() + 1}月`,
            };
        case 3:
            return {
                title: '选择年',
                options: Array.from({ length: 5 }, (_, index) => ({
                    text: `${new Date().getFullYear() - index}年`,
                    value: new Date().getFullYear() - index,
                })),
                defaultValue: new Date().getFullYear(),
                defaultText: `${new Date().getFullYear()}年`,
            };
        default:
            return {
                defaultValue: 0,
            };
    }
}