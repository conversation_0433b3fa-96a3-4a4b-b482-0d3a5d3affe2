export function getPieOptions(data, total) {
    return {
        tooltip: {
            trigger: 'item',
            formatter: '{b}: {d}%',
        },
        legend: {
            orient: 'vertical',
            right: '10%',
            top: 'center',
            icon: 'circle',
            itemWidth: 8,
            itemHeight: 8,
            itemGap: 30,
            formatter: function (name) {
                const item = data.find((item) => item.name === name);
                return `${name} ${item?.rate}% (${item?.value}/${total})`;
            },
        },
        series: [
            {
                name: '占比',
                type: 'pie',
                radius: ['45%', '80%'],
                center: ['25%', '50%'],
                avoidLabelOverlap: false,
                label: {
                    show: false,
                    position: 'center',
                },
                labelLine: {
                    show: false,
                },
                color: [
                    {
                        type: 'linear',
                        x: 1,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [
                            { offset: 0, color: '#FAC858' },
                            { offset: 1, color: '#F9F77C' },
                        ],
                    },
                    {
                        type: 'linear',
                        x: 1,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [
                            { offset: 0, color: '#2C7DFF' },
                            { offset: 1, color: '#89A6EA' },
                        ],
                    },
                    {
                        type: 'linear',
                        x: 1,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [
                            { offset: 0, color: '#40DC74' },
                            { offset: 1, color: '#B2F1E2' },
                        ],
                    },
                ],
                data,
            },
        ],
    };
}