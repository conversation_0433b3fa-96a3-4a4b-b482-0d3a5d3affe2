<script setup>
import { useRouter } from 'vue-router';
import { ref } from 'vue';
import { showToast } from 'vant';
import Segment from '@/components/Segment.vue';
import { getRankFontColor, getDurationStr } from './utils';
import { getCourseRank, getCourseFavoriteRank } from '@/api/profile';
import CourseCategory from '@/assets/profile/courseCategory.svg';
import CourseUser from '@/assets/profile/courseUser.svg';

const router = useRouter();

const sortType = ref(0);
const dateType = ref('month');
const loading = ref(false);
const courseRank = ref([]);

function handleChangeTab() {
  if (sortType.value === 0) {
    getCourseCountRankList();
  } else {
    getCourseFavoriteRankList();
  }
}

// 获取课程学习次数排行
async function getCourseCountRankList() {
  try {
    loading.value = true;
    const [startTime, endTime] = getDurationStr(dateType.value);
    const res = await getCourseRank({ startTime, endTime, limit: 100 });
    if (res.code !== 0) throw new Error(res.msg);
    courseRank.value = res.data;
  } catch (error) {
    showToast(error.message);
  } finally {
    loading.value = false;
  }
}

// 获取课程收藏数排行
async function getCourseFavoriteRankList() {
  try {
    loading.value = true;
    const [startTime, endTime] = getDurationStr(dateType.value);
    const res = await getCourseFavoriteRank({ startTime, endTime, limit: 100 });
    if (res.code !== 0) throw new Error(res.msg);
    courseRank.value = res.data;
  } catch (error) {
    showToast(error.message);
  } finally {
    loading.value = false;
  }
}

getCourseCountRankList();
</script>

<template>
  <div class="flex flex-col overflow-hidden" style="background: linear-gradient(180deg, #ccdafc 0%, #ffffff 15%)">
    <van-config-provider :theme-vars="{ navBarBackground: 'transparent' }">
      <van-nav-bar :border="false" left-arrow left-text="返回" @click-left="router.back()"> </van-nav-bar>
    </van-config-provider>

    <van-dropdown-menu>
      <van-dropdown-item
        v-model="sortType"
        :options="[
          { text: '观看次数', value: 0 },
          { text: '收藏数', value: 1 },
        ]"
        @change="handleChangeTab"
      />
    </van-dropdown-menu>

    <div class="mt-[16px] px-[48px] font-youShe text-[72px]">课程排行榜</div>

    <segment
      class="mx-[32px] mt-[16px]"
      rounded
      :segments="[
        { label: '月', value: 'month' },
        { label: '年', value: 'year' },
      ]"
      v-model:active-key="dateType"
      @change="handleChangeTab"
    />

    <div v-if="!loading && courseRank.length > 0" class="flex-1 flex flex-col gap-[32px] py-[32px] mx-[32px] overflow-auto">
      <div v-for="(course, index) in courseRank" :key="course.courseId" class="flex items-center gap-[16px]">
        <span class="text-[32px] font-youShe w-[70px] text-center" :style="{ color: getRankFontColor(index) }">{{ index + 1 }}</span>
        <div class="flex-1 flex gap-[16px]">
          <img class="w-[248px] h-[140px] rounded-[16px] overflow-hidden" :src="course.thumb" alt="" />
          <div class="flex-1 flex flex-col h-[140px]">
            <span class="text-[28px] w-[336px] van-ellipsis">{{ course.courseName }}</span>
            <div class="flex items-center mt-[5px]">
              <img :src="CourseCategory" class="w-[24px] mr-[10px]" />
              <span class="text-[24px] text-desc van-ellipsis">{{ course.categoryName }}</span>
            </div>
            <div class="flex items-center mt-[5px]">
              <img :src="CourseUser" class="h-[30px] mr-[10px]" />
              <span class="flex-1 text-[24px] text-desc">{{ course.teacher }}</span>
              <img src="@/assets/profile/show.png" class="w-[30px] mr-[10px]" />
              <span class="text-[24px] text-desc">{{ course.count }}次</span>
            </div>
          </div>
        </div>
      </div>
      <div class="flex justify-center items-center h-[30px] text-[28px] text-desc">没有更多了</div>
    </div>

    <van-skeleton v-if="loading" class="mt-[24px]" title :row="6" />
    <div v-if="!loading && courseRank.length === 0" class="flex justify-center items-center h-[800px] text-[28px] text-desc">暂无数据</div>
  </div>
</template>

<style scoped>
:deep(.van-dropdown-menu__bar) {
  height: 70px;
  box-shadow: none;
  background-color: transparent;

  .van-dropdown-menu__title {
    font-size: 28px;
    color: #333;
  }
}
</style>
