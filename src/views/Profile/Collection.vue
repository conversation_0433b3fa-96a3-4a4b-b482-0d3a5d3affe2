<script setup>
import { useRouter } from 'vue-router';
import { ref, computed } from 'vue';
import CourseCard from './components/CourseCard.vue';
import { showConfirmDialog, showToast } from 'vant';
import { getFavorites, cancelFavorite } from '@/api/profile';

const router = useRouter();
const actions = [{ name: '取消收藏', color: '#ee0a24' }];

const searchValue = ref('');
const showPopup = ref(false);
const isEditing = ref(false);

const loading = ref(false);
const courseList = ref([]);
const isEmpty = computed(() => courseList.value.length === 0);
// 选中项的数组
const selectedItems = ref([]);
// 是否全选
const isAllSelected = computed(() => {
  return courseList.value.length > 0 && selectedItems.value.length === courseList.value.length;
});

// 切换全选状态
const toggleSelectAll = () => {
  if (isAllSelected.value) {
    selectedItems.value = [];
  } else {
    selectedItems.value = courseList.value.map((item) => item.id);
  }
};

// 切换单个选择状态
const toggleSelect = (id) => {
  const index = selectedItems.value.indexOf(id);
  if (index === -1) {
    selectedItems.value.push(id);
  } else {
    selectedItems.value.splice(index, 1);
  }
};

const handleEdit = () => {
  if (isEditing.value) {
    isEditing.value = false;
    selectedItems.value = [];
  } else {
    isEditing.value = true;
  }
};

const onDeleteClick = () => {
  if (selectedItems.value.length === 0) {
    showToast('请选择要取消收藏的课程');
    return;
  }
  cancelFavoriteCourse(selectedItems.value);
};

// 获取收藏课程
async function getFavoritesCourse() {
  try {
    loading.value = true;
    const res = await getFavorites();
    if (res.code !== 0) throw new Error(res.msg);
    courseList.value = res.data;
  } catch (error) {
    showToast(error.message);
  } finally {
    loading.value = false;
  }
}

// 取消收藏
async function cancelFavoriteCourse(ids) {
  showConfirmDialog({
    title: '确定取消收藏吗？',
  })
    .then(async () => {
      try {
        const res = await cancelFavorite(ids);
        if (res.code !== 0) throw new Error(res.msg);
        showToast('已取消收藏');
        getFavoritesCourse();
        selectedItems.value = [];
      } catch (error) {
        showToast(error.message);
      }
    })
    .catch(() => {
      // 取消操作
      selectedItems.value = [];
      isEditing.value = false;
    });
}

getFavoritesCourse();
</script>

<template>
  <div class="h-full flex flex-col bg-white">
    <van-nav-bar title="我的收藏" left-arrow left-text="返回" @click-left="router.back()">
      <template #right>
        <span v-if="!isEmpty" class="text-primary" @click="handleEdit">{{ isEditing ? '取消' : '编辑' }}</span>
      </template>
    </van-nav-bar>
    <!-- <van-search v-if="!isEmpty" v-model="searchValue" placeholder="搜索课程名称、讲师" /> -->
    <div class="flex-1 overflow-auto px-[32px] py-[32px] flex flex-col gap-4">
      <template v-if="!loading && courseList.length > 0">
        <div v-for="item in courseList" :key="item.id" class="flex items-center gap-2">
          <van-checkbox v-show="isEditing" :model-value="selectedItems.includes(item.id)" @click="toggleSelect(item.id)" icon-size="16px" />
          <CourseCard class="flex-1" :url="item.thumb" :title="item.title" :category="item.categoryVo.name" :teacher="item.teacher">
            <template #default>
              <van-icon
                name="ellipsis"
                @click="
                  () => {
                    selectedItems = [item.id];
                    showPopup = true;
                  }
                "
              />
            </template>
          </CourseCard>
        </div>
        <div class="flex justify-center items-center h-[30px] text-[28px] text-desc">没有更多了</div>
      </template>
      <van-skeleton v-if="loading" class="mt-[32px]" title :row="8" />
      <div v-if="!loading && isEmpty" class="flex justify-center items-center h-[800px] text-[28px] text-desc">暂无数据</div>
    </div>
    <!-- 全选区域 -->
    <div v-if="isEditing && !isEmpty" class="flex items-center justify-between mb-[16px] p-[32px] bg-white">
      <van-checkbox v-model="isAllSelected" @click="toggleSelectAll" icon-size="16px">全选</van-checkbox>
      <span class="text-delete" @click="onDeleteClick">删除</span>
    </div>
    <!-- 取消收藏确认 -->
    <van-action-sheet
      v-model:show="showPopup"
      :actions="actions"
      cancel-text="取消"
      @select="onDeleteClick"
      @colse="selectedItems = []"
      @cancel="selectedItems = []"
      close-on-click-action
    />
  </div>
</template>
