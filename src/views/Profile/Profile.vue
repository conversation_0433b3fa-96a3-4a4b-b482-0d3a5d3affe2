<script setup>
import IconMyFile from '@/assets/profile/my-file.png';
import IconMyStar from '@/assets/profile/my-star.png';
import IconStatistics from '@/assets/profile/my-statistics.png';
import IconGold from '@/assets/profile/gold.png';
import IconSilver from '@/assets/profile/silver.png';
import IconBronze from '@/assets/profile/bronze.png';
import { backClient } from '@/utils';
import { useRouter } from 'vue-router';
import { ref } from 'vue';
import { showToast } from 'vant';
import { getBaseInfo, getRecentCourses, getDurationRank, getCourseRank } from '@/api/profile';
import ProfileStatistics from './components/ProfileStatistics.vue';
import { formatDuration, getRankFontColor, formatStudyDuration, getDurationStr } from './utils';
import Segment from '@/components/Segment.vue';

const router = useRouter();

const rankTab = ref(0); // 排行榜

const baseInfo = ref({}); // 学习信息
const rankLoading = ref(false);
const isCourseLoading = ref(false);
const durationRank = ref(); // 学员时长排行
const courseRank = ref([]); // 课程学习次数排行
const recentCourses = ref([]); // 最近学习课程

// 获取学习信息
async function getStudyInfo() {
  try {
    const [startTime, endTime] = getDurationStr('year');
    const res = await getBaseInfo(startTime, endTime);
    if (res.code !== 0) throw new Error(res.msg);
    baseInfo.value = res.data;
  } catch (error) {
    showToast('获取学习信息失败');
  }
}

// 获取最近学习课程
async function getRecentCoursesList() {
  try {
    isCourseLoading.value = true;
    const [startTime, endTime] = getDurationStr('lastMonth');
    const res = await getRecentCourses(startTime, endTime, 10);
    if (res.code !== 0) throw new Error(res.msg);
    Object.keys(res.data.inPlan).forEach((key) => {
      res.data.inPlan[key].forEach((item) => {
        recentCourses.value.push(item);
      });
    });
  } catch (error) {
    showToast(error.message);
  } finally {
    isCourseLoading.value = false;
  }
}

// 获取学员时长排行
async function getUserDurationRank() {
  try {
    rankLoading.value = true;
    const [startTime, endTime] = getDurationStr('month');
    const res = await getDurationRank({ startTime, endTime, limit: 3, areaType: 0 });
    if (res.code !== 0) throw new Error(res.msg);
    durationRank.value = res.data;
  } catch (error) {
    showToast(error.message);
  } finally {
    rankLoading.value = false;
  }
}

// 获取课程学习次数排行
async function getCourseCountRank() {
  try {
    const [startTime, endTime] = getDurationStr('month');
    const res = await getCourseRank({ startTime, endTime, limit: 3 });
    if (res.code !== 0) throw new Error(res.msg);
    courseRank.value = res.data;
  } catch (error) {
    showToast(error.message);
  }
}

getStudyInfo();
getRecentCoursesList();
getUserDurationRank();
getCourseCountRank();

// 更多入口配置
const moreEntryList = [
  [
    {
      icon: IconMyFile,
      title: '我的附件',
      page: '/my-attachment',
    },
    {
      icon: IconMyStar,
      title: '我的收藏',
      page: '/profile/collection',
    },
  ],
  [
    {
      icon: IconStatistics,
      title: '培训统计',
      page: '/profile/statistics',
    },
  ],
];
</script>

<template>
  <div class="flex flex-col overflow-hidden profile-bg">
    <van-config-provider :theme-vars="{ navBarBackground: 'transparent' }">
      <van-nav-bar class="bg-transparent" title="培训系统" :border="false" left-arrow left-text="返回" @click-left="backClient" />
    </van-config-provider>

    <div class="flex-1 overflow-auto px-[32px] pb-[32px]">
      <!-- 个人信息 -->
      <div class="flex items-center gap-[32px] py-[16px]">
        <img :src="baseInfo?.avatar" class="h-[112px] w-[112px] rounded-full" alt="用户头像" />
        <span>
          <div class="flex items-center gap-[16px]">
            <span class="font-medium text-[32px]">{{ baseInfo?.userName }}</span>
            <span
              v-if="baseInfo?.permission"
              class="px-[12px] py-[4px] text-[20px] leading-[20px] text-primary border border-primary rounded-[8px]"
              >{{ baseInfo?.permission }}</span
            >
          </div>
          <span class="text-[24px] text-desc">{{ baseInfo?.deptName }}</span>
        </span>
      </div>

      <!-- 学习统计 TODO: 数据长度限制如 999+ -->
      <profile-statistics :baseInfo="baseInfo" />

      <!-- 最近观看 -->
      <div class="p-[32px] bg-white rounded-[16px] mt-[16px]">
        <div class="flex justify-between items-center" @click="router.push('/profile/recent-viewed')">
          <span class="flex-1 text-[32px] font-medium">最近观看</span>
          <span class="text-[24px] text-desc">查看更多</span>
          <img src="@/assets/profile/arrow-right.svg" alt="排行榜 Icon" class="ml-[12px] w-[13px]" />
        </div>
        <div v-if="!isCourseLoading && recentCourses.length > 0" class="flex flex-nowrap overflow-x-auto gap-[32px] mt-[24px]">
          <div
            class="w-[248px] flex-shrink-0"
            v-for="course in recentCourses"
            :key="course.id"
            @click="router.push(`/details/${course.courseId}/${course.planId}`)"
          >
            <div class="relative w-full h-[140px] rounded-[16px] overflow-hidden">
              <img :src="course?.thumb" alt="课程封面" class="w-full h-full" />
              <div
                class="absolute bottom-0 left-0 right-0 h-[50px] bg-gradient-to-t from-black/60 to-transparent flex items-center justify-end px-[16px]"
              >
                <span class="text-white text-[24px]">{{
                  course.isFinish
                    ? '已看完'
                    : `${formatDuration(course.currentHourFinishDuration || 0)}/${formatDuration(course.currentHourTotalDuration || 0)}`
                }}</span>
              </div>
            </div>
            <span class="mt-[16px] text-[28px] van-multi-ellipsis--l2">{{ course.courseName }}</span>
          </div>
        </div>
        <van-skeleton v-if="isCourseLoading" title :row="3" />
        <div v-if="!isCourseLoading && recentCourses.length === 0" class="flex justify-center items-center h-[200px] text-[28px] text-desc">
          暂无数据
        </div>
      </div>

      <!-- 培训排行榜 -->
      <div class="mt-[20px] p-[32px] bg-white rounded-[16px]">
        <div class="flex justify-between items-center">
          <img src="@/assets/profile/fire.png" alt="排行榜 Icon" class="w-[40px] mr-[8px]" />
          <span class="flex-1 font-medium text-[32px]">培训排行榜</span>
          <segment
            class="w-[296px]"
            :segments="[
              { label: '学员榜', value: 0 },
              { label: '课程榜', value: 1 },
            ]"
            v-model:active-key="rankTab"
          />
        </div>

        <div class="mt-[32px] flex flex-col text-[28px]" :class="rankTab === 0 ? 'gap-[32px]' : 'gap-[24px]'">
          <!-- 学员榜 -->
          <template v-if="rankTab === 0 && durationRank?.studyTimeRankingUserVos">
            <div class="flex items-center gap-[16px]" v-for="user in durationRank?.studyTimeRankingUserVos" :key="user.id">
              <span class="w-[40px] text-center text-[40px] font-youShe" :style="{ color: getRankFontColor(user.num) }">{{ user.num + 1 }}</span>
              <div class="relative w-fit">
                <img class="align-middle w-[112px] h-[112px] rounded-full" :src="user.avatar" alt="" />
                <img v-if="user.num === 0" :src="IconGold" alt="" class="absolute right-[-4px] top-[-20px] w-[40px] h-[40px]" />
                <img v-if="user.num === 1" :src="IconSilver" alt="" class="absolute right-[-4px] top-[-20px] w-[40px] h-[40px]" />
                <img v-if="user.num === 2" :src="IconBronze" alt="" class="absolute right-[-4px] top-[-20px] w-[40px] h-[40px]" />
              </div>
              <span class="flex-1">{{ user.userName }}</span>
              <span style="color: rgba(0, 0, 0, 0.4)">{{ formatStudyDuration(user.studyTime) }}</span>
            </div>
          </template>

          <!-- 课程榜 -->
          <template v-if="rankTab === 1 && courseRank.length">
            <div
              class="flex items-center gap-[16px]"
              v-for="(course, index) in courseRank"
              :key="course.id"
              @click="router.push(`/details/${course.courseId}`)"
            >
              <div class="relative w-fit rounded-[16px] overflow-hidden">
                <img class="align-middle w-[248px] h-[140px]" :src="course.thumb" alt="" />
                <div
                  class="absolute left-0 top-0 w-[48px] leading-[52px] text-white text-center font-youShe rounded-br-[20px]"
                  :style="{ backgroundColor: getRankFontColor(index) }"
                >
                  {{ index + 1 }}
                </div>
              </div>

              <div class="flex-1 flex flex-col h-[140px]">
                <span class="flex-1 van-multi-ellipsis--l2">{{ course.courseName }}</span>
                <div class="flex items-center mt-[16px]">
                  <img src="@/assets/profile/show.png" alt="观看次数 Icon" class="h-[30px] mr-[10px]" />
                  <span class="text-[24px] text-desc">{{ course.count }}次</span>
                </div>
              </div>
            </div>
          </template>
        </div>

        <div
          v-if="!rankLoading && (durationRank?.studyTimeRankingUserVos?.length || courseRank.length)"
          @click="
            () => {
              if (rankTab === 0) {
                router.push('/profile/user-rank');
              } else {
                router.push('/profile/course-rank');
              }
            }
          "
          class="flex items-center justify-center mt-[24px] gap-[12px]"
        >
          <span class="text-[24px] text-desc">查看更多</span>
          <img src="@/assets/profile/arrow-right.svg" alt="排行榜 Icon" class="w-[13px]" />
        </div>

        <van-skeleton v-if="rankLoading" title :row="4" />
      </div>

      <!-- 更多入口 -->
      <div v-for="(entryList, index) in moreEntryList" :key="index" class="mt-[24px] px-[32px] bg-white rounded-[16px] last:border-none">
        <div
          v-for="entry in entryList"
          :key="entry.title"
          @click="router.push(entry.page)"
          class="py-[32px] flex items-center justify-between border-b border-solid last:border-none"
          style="border-color: rgba(0, 0, 0, 0.05)"
        >
          <img :src="entry.icon" alt="" class="h-[40px] mr-[16px]" />
          <span class="flex-1 text-[28px] font-medium">{{ entry.title }}</span>
          <img src="@/assets/profile/arrow-right.svg" alt="排行榜 Icon" class="w-[16px]" />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.profile-bg {
  background:
    url('@/assets/profile/bg.png') calc(100% + 100px) calc(0% - 150px) / 317px 317px no-repeat,
    linear-gradient(to bottom, #edf0f6, #f5f6f7);
}
</style>
