<script setup>
import { useRouter } from 'vue-router';
import Segment from '@/components/Segment.vue';
import IconCourse from '@/assets/profile/learning-statistics/statistics-course.png';
import IconFinish from '@/assets/profile/learning-statistics/statistics-finish.png';
import IconDuration from '@/assets/profile/learning-statistics/statistics-duration.png';
import IconRate from '@/assets/profile/learning-statistics/statistics-rate.png';
import ProfileStatistics from './components/ProfileStatistics.vue';
import { ref, onMounted, computed, watch } from 'vue';
import { getBaseInfo, getPersonStatistics } from '@/api/profile';
import { getDurationStr, getOptionsFromStudyTimes } from './utils';
import { showToast } from 'vant';
import * as echarts from 'echarts';

const router = useRouter();

const chartOptions = {
  grid: {
    left: '4%',
    top: '5%',
    bottom: '12%',
    containLabel: true,
  },
  xAxis: {
    type: 'category',
    data: ['一', '二', '三', '四', '五', '六', '日'],
    axisTick: {
      show: false,
    },
    axisLine: {
      show: false,
    },
    axisLabel: {
      show: true,
      formatter: '{value}',
    },
  },
  yAxis: {
    type: 'value',
    show: true,
    splitLine: {
      lineStyle: {
        color: '#EEEEEE',
        type: 'dashed',
      },
    },
    axisLine: {
      show: false,
    },
    axisTick: {
      show: false,
    },
    max: function (value) {
      return Math.ceil(value.max);
    },
    axisLabel: {
      show: true,
      showMaxLabel: true,
      showMinLabel: true,
      formatter: function (value, index) {
        if (value === 0) return '0';
        const max = Math.max(...chartOptions.series[0].data);
        const roundedMax = Math.ceil(max);

        // 将毫秒转换为分钟
        if (value === roundedMax) {
          const minutes = Math.round(value / 1000 / 60);
          return minutes;
        }
        return '';
      },
    },
  },
  series: [
    {
      data: [0, 0, 0, 0, 0, 0, 0],
      type: 'bar',
      barWidth: 10,
      itemStyle: {
        borderRadius: [219, 219, 219, 219],
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          {
            offset: 0,
            color: 'rgba(15, 90, 240, 1)',
          },
          {
            offset: 1,
            color: 'rgba(135, 137, 244, 1)',
          },
        ]),
      },
    },
  ],
};

const dateList = [
  {
    title: '周',
    type: 1,
    studyTimeTitle: '本周每日学习时长',
  },
  {
    title: '月',
    type: 2,
    studyTimeTitle: '本月每周学习时长',
  },
  {
    title: '年',
    type: 3,
    studyTimeTitle: '本年每月学习时长',
  },
  // {
  //   title: '总',
  //   type: 0,
  //   studyTimeTitle: '年度学习时长',
  // },
];

const tabActive = ref(1);
const pickerData = ref(getOptionsFromStudyTimes(tabActive.value));
const weekSelect = ref(pickerData.value.defaultValue);
const weekSelectText = ref(pickerData.value.defaultText);
const showPicker = ref(false);

const baseInfo = ref({});
const statistics = ref({});

const chartRef = ref(null);
let chartInstance = null;

onMounted(() => {
  chartInstance = echarts.init(chartRef.value);
  chartInstance.setOption(chartOptions);
});

watch(
  () => statistics.value,
  () => {
    const { xData, seriesData, xAxisLabelFormatter } = formatStudyTimes(tabActive.value, statistics.value?.studyTimes || []);
    chartOptions.xAxis.data = xData;
    chartOptions.series[0].data = seriesData;
    chartOptions.xAxis.axisLabel.formatter = xAxisLabelFormatter;
    chartInstance.setOption(chartOptions);
  },
);

const info = computed(() => [
  {
    key: 0,
    icon: IconCourse,
    title: '看过的课程',
    value: statistics.value?.courseNum || 0,
    unit: '节',
  },
  {
    key: 1,
    icon: IconFinish,
    title: '完成的课程',
    value: statistics.value?.finishCourseNum || 0,
    unit: '节',
  },
  {
    key: 2,
    icon: IconDuration,
    title: '单日最高时长',
    value: Math.round((statistics.value?.maxDuration || 0) / 1000 / 60),
    unit: '分钟',
  },
  {
    key: 3,
    icon: IconRate,
    title: '合格率',
    value: Math.round(statistics.value?.completion || 0),
    unit: '%',
  },
]);

const totalDuration = computed(() => {
  const duration = statistics.value?.totalDuration || 0;
  return {
    hour: Math.floor(duration / 1000 / 60 / 60),
    min: Math.round((duration % (1000 * 60 * 60)) / (1000 * 60)),
  };
});

// 获取学习信息
async function getStudyInfo() {
  try {
    const [startTime, endTime] = getDurationStr('year');
    const res = await getBaseInfo(startTime, endTime);
    if (res.code !== 0) throw new Error(res.msg);
    baseInfo.value = res.data;
  } catch (error) {
    showToast('获取学习信息失败');
  }
}

// 获取统计数据
async function getPersonData() {
  try {
    const res = await getPersonStatistics(tabActive.value, weekSelect.value);
    if (res.code !== 0) {
      throw new Error(res.msg);
    }
    statistics.value = res.data;
  } catch (e) {
    console.log(e);
  }
} // 确保这里返回的是毫秒值

const weekTitle = ['日', '一', '二', '三', '四', '五', '六'];

function formatStudyTimes(type, studyTimes) {
  return {
    xData: type === 1 ? studyTimes.map((item) => weekTitle[new Date(item.subscript).getDay()]) : studyTimes.map((item) => item.subscript),
    seriesData: studyTimes.map((item) => item.duration),
    xAxisLabelFormatter: type === 2 ? '第{value}周' : '{value}',
  };
}

function handleTabChange() {
  pickerData.value = getOptionsFromStudyTimes(tabActive.value);
  weekSelect.value = pickerData.value.defaultValue;
  weekSelectText.value = pickerData.value.defaultText;
  getPersonData();
}

function handlePickerChange({ selectedValues, selectedOptions }) {
  console.log(selectedValues, selectedOptions);
  const { text, value } = selectedOptions[0];
  weekSelectText.value = text;
  weekSelect.value = value;
  showPicker.value = false;
  getPersonData();
}

getStudyInfo();
getPersonData();
</script>

<template>
  <div class="flex flex-col overflow-hidden" style="background: linear-gradient(180deg, #ccdafc 0%, #ffffff 20%)">
    <van-config-provider :theme-vars="{ navBarBackground: 'transparent' }">
      <van-nav-bar class="bg-transparent" title="培训系统" :border="false" left-arrow left-text="返回" @click-left="router.back()" />
    </van-config-provider>

    <div class="flex-1 overflow-auto p-[32px] pt-[16px]">
      <profile-statistics :baseInfo="baseInfo" :is-card="true" />

      <segment
        :segments="[
          { label: '周', value: 1 },
          { label: '月', value: 2 },
          { label: '年', value: 3 },
        ]"
        class="mt-[32px] w-full"
        :rounded="true"
        v-model:activeKey="tabActive"
        @change="handleTabChange"
      />

      <div class="mt-[32px] bg-white rounded-[16px] shadow-card">
        <div class="p-[32px]">
          <div class="flex justify-between items-center">
            <span class="text-[32px] font-medium">时长分布</span>
            <span @click="showPicker = true" class="text-[28px]">{{ weekSelectText }}</span>
          </div>

          <div class="mt-[8px]">
            <span class="mr-[4px] text-[40px] font-medium">{{ totalDuration.hour }}</span>
            <span class="mr-[8px] text-[28px]">小时</span>
            <span class="mr-[4px] text-[40px] font-medium">{{ totalDuration.min }}</span>
            <span class="text-[28px]">分钟</span>
            <div class="mt-[4px] text-[24px] text-desc">{{ dateList.find((data) => data.type === tabActive)?.studyTimeTitle }}</div>
          </div>
        </div>

        <div ref="chartRef" class="h-[400px] w-full"></div>
      </div>

      <div class="mt-[32px] grid grid-cols-2 gap-[32px]">
        <div v-for="item in info" :key="item.key" class="box-border p-[32px] bg-white rounded-[16px] shadow-card">
          <div class="flex items-center gap-2">
            <img :src="item.icon" alt="Icon" class="w-[40px] h-[40px]" />
            <span class="text-desc text-[28px]">{{ item.title }}</span>
          </div>
          <div class="mt-[16px]">
            <span class="text-[40px] font-medium">{{ item.value }}</span>
            <span class="ml-[4px] text-[24px]">{{ item.unit }}</span>
          </div>
        </div>
      </div>
    </div>

    <van-popup v-model:show="showPicker" destroy-on-close round position="bottom">
      <van-picker :model-value="[weekSelect]" :columns="pickerData.options" @cancel="showPicker = false" @confirm="handlePickerChange" />
    </van-popup>
  </div>
</template>

<style scoped>
.study-stats {
  flex: 1;
  text-align: center;

  .number {
    font-size: 20px;
    font-weight: 500;
    line-height: 26px;
  }

  .unit {
    margin-left: 2px;
    font-size: 12px;
  }

  .desc {
    font-size: 12px;
    color: #878b95;
  }
}

.shadow-card {
  box-shadow: 0px 8px 24px 0px rgba(149, 157, 165, 0.2);
}
</style>
