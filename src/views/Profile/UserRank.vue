<script setup>
import { useRouter } from 'vue-router';
import Segment from '@/components/Segment.vue';
import IconGold from '@/assets/profile/gold.png';
import IconSilver from '@/assets/profile/silver.png';
import IconBronze from '@/assets/profile/bronze.png';
import { ref, computed } from 'vue';
import { showToast } from 'vant';
import { getDurationRank, getCompletionRateRank } from '@/api/profile';
import { formatStudyDuration, getDurationStr, getRankFontColor } from './utils';

const DEFAULT_AVATAR = 'https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg';

const router = useRouter();

const sortType = ref(0);
const areaType = ref(0);
const dateType = ref('month');
const loading = ref(false);

const rankInfo = ref({ isInRanking: false, myRank: 1, myInfo: null });
const userRank = ref([]);
const topUsers = computed(() => userRank.value.slice(0, 3));

// 获取学员时长排行
async function getUserDurationRank() {
  try {
    loading.value = true;
    const [startTime, endTime] = getDurationStr(dateType.value);
    const res = await getDurationRank({
      startTime,
      endTime,
      limit: 100,
      areaType: areaType.value,
    });
    if (res.code !== 0) throw new Error(res.msg);
    const { myRank, isInRanking, studyTimeRankingUserVos, myInfo } = res.data;
    rankInfo.value = { myRank, isInRanking, myInfo };
    userRank.value = studyTimeRankingUserVos;
  } catch (error) {
    showToast(error.message);
  } finally {
    loading.value = false;
  }
}

// 获取学员合格率排行
async function getUserCompletionRateRank() {
  try {
    loading.value = true;
    const [startTime, endTime] = getDurationStr(dateType.value);
    const res = await getCompletionRateRank({
      startTime,
      endTime,
      limit: 100,
      areaType: areaType.value,
    });
    if (res.code !== 0) throw new Error(res.msg);
    const { myRank, isInRanking, passRateRankingUserVos } = res.data;
    rankInfo.value = { myRank, isInRanking };
    userRank.value = passRateRankingUserVos;
  } catch (error) {
    showToast(error.message);
  } finally {
    loading.value = false;
  }
}

function handleChangeTab() {
  if (sortType.value === 0) {
    getUserDurationRank();
  } else {
    getUserCompletionRateRank();
  }
}

function handleCheckMyRank() {
  if (!rankInfo.value.isInRanking) return showToast('您未上榜');
  if (rankInfo.value.myRank < 3) return;
  const dom = document.getElementById(rankInfo.value.myInfo.userId);
  if (dom) {
    dom.scrollIntoView({ behavior: 'smooth', block: 'center' });
    // 添加闪烁效果
    dom.classList.add('highlight-flash');
    // 3秒后移除闪烁效果
    setTimeout(() => {
      dom.classList.remove('highlight-flash');
    }, 3000);
  }
}

getUserDurationRank();
</script>

<template>
  <div class="flex flex-col overflow-hidden" style="background: linear-gradient(180deg, #ccdafc 0%, #ffffff 15%)">
    <van-config-provider :theme-vars="{ navBarBackground: 'transparent' }">
      <van-nav-bar :border="false" left-arrow left-text="返回" @click-left="router.back()"> </van-nav-bar>
    </van-config-provider>

    <van-dropdown-menu>
      <van-dropdown-item
        v-model="sortType"
        :options="[
          { text: '观看时长', value: 0 },
          { text: '合格率', value: 1 },
        ]"
        @change="handleChangeTab"
      />
      <!-- <van-dropdown-item
        v-model="areaType"
        :options="[
          { text: '所属部门', value: 1 },
          { text: '所属园区', value: 2 },
          { text: '集团公司', value: 0 },
        ]"
        @change="handleChangeTab"
      /> -->
    </van-dropdown-menu>

    <div class="mt-[16px] px-[48px] font-youShe text-[72px]">学员排行榜</div>

    <segment
      class="mx-[32px] mt-[16px]"
      rounded
      :segments="[
        { label: '月', value: 'month' },
        { label: '年', value: 'year' },
      ]"
      v-model:active-key="dateType"
      @change="handleChangeTab"
    />

    <div class="flex-1 mt-[16px] py-[24px] overflow-auto">
      <div v-if="!loading" class="flex justify-between items-end px-[64px]">
        <div v-for="item in [1, 0, 2]" :key="item" class="flex flex-col items-center">
          <div class="relative">
            <img v-if="item === 0" :src="IconGold" alt="" class="absolute right-[-5px] top-[-20px] w-[50px] h-[50px]" />
            <img v-if="item === 1" :src="IconSilver" alt="" class="absolute right-[-10px] top-[-25px] w-[50px] h-[50px]" />
            <img v-if="item === 2" :src="IconBronze" alt="" class="absolute right-[-10px] top-[-25px] w-[50px] h-[50px]" />
            <img
              class="rounded-full"
              :class="item === 0 ? 'w-[144px] h-[144px]' : 'w-[112px] h-[112px]'"
              :src="topUsers[item]?.avatar || DEFAULT_AVATAR"
              alt=""
            />
          </div>
          <div class="mt-[16px] flex items-center gap-[10px]">
            <span class="font-youShe text-[32px] leading-normal" :style="{ color: getRankFontColor(item) }">{{ item + 1 }}.</span>
            <span class="text-[28px]">{{ topUsers[item]?.userName || '虚位以待' }}</span>
          </div>
          <div v-if="sortType === 0" class="text-[24px] text-desc">{{ formatStudyDuration(topUsers[item]?.studyTime || 0) }}</div>
          <div v-if="sortType === 1" class="text-[24px] text-desc">{{ ((topUsers[item]?.passRate || 0) * 100).toFixed(0) }}%</div>
        </div>
      </div>
      <van-skeleton v-if="loading" title :row="3" />

      <div
        class="mt-[32px] px-[32px] py-[16px] flex flex-col gap-[32px] rounded-[32px] bg-white"
        style="box-shadow: 0px -8px 48px 0px rgba(0, 0, 0, 0.05)"
      >
        <template v-if="!loading">
          <div v-for="item in userRank.slice(3)" :key="item.userId" :id="item.userId" class="flex items-center gap-[16px]">
            <div class="font-youShe text-[32px]">{{ item.num + 1 }}</div>
            <img class="w-[80px] h-[80px] rounded-full" :src="item.avatar || DEFAULT_AVATAR" alt="" />
            <div class="flex-1 text-[28px]">{{ item.userName }}</div>
            <div v-if="sortType === 0" class="text-[24px] text-desc">{{ formatStudyDuration(item.studyTime) }}</div>
            <div v-if="sortType === 1" class="text-[24px] text-desc">{{ (item.passRate * 100).toFixed(0) }}%</div>
          </div>
        </template>
        <van-skeleton v-if="loading" title :row="20" />
        <div v-if="!loading && userRank.length < 3" class="flex justify-center items-center h-[800px] text-[28px] text-desc">暂无数据</div>
      </div>

      <div
        v-if="rankInfo.myInfo"
        @click="handleCheckMyRank"
        class="fixed bottom-[60px] left-[32px] right-[32px] px-[32px] py-[16px] flex items-center gap-[16px] rounded-full bg-[#EDEDEE]"
        style="box-shadow: 0px 16px 48px 0px rgba(149, 157, 165, 0.2)"
      >
        <div class="font-youShe text-[32px]">{{ rankInfo.isInRanking ? rankInfo.myRank + 1 : '100+' }}</div>
        <img class="w-[80px] h-[80px] rounded-full" :src="rankInfo.myInfo?.avatar || DEFAULT_AVATAR" alt="" />
        <div class="flex-1 text-[28px]">{{ rankInfo.myInfo?.userName }}</div>
        <div class="text-[24px] text-desc">{{ formatStudyDuration(rankInfo.myInfo?.studyTime || 0) }}</div>
      </div>
    </div>
  </div>
</template>

<style scoped>
:deep(.van-dropdown-menu__bar) {
  height: 70px;
  box-shadow: none;
  background-color: transparent;

  .van-dropdown-menu__title {
    font-size: 28px;
    color: #333;
  }
}

/* 添加闪烁动画效果 */
@keyframes flash {
  0%, 100% { background-color: transparent; }
  50% { background-color: rgba(204, 218, 252, 0.5); }
}

.highlight-flash {
  animation: flash 0.6s ease-in-out 4;
  border-radius: 16px;
}
</style>
