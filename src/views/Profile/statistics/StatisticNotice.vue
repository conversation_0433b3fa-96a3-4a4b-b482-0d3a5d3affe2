<script setup>
import { ref } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { showFailToast, showToast } from 'vant';
import { sendNotification } from '@/api/profile';

const router = useRouter();
const route = useRoute();
const { planId } = route.query;

const message = ref('');
const showBottom = ref(false);
const selectedStatus = ref([]); // 选中的状态
const noticeLoading = ref(false);

// 状态选项
const statusOptions = [
  { text: '未参加', value: 1 },
  { text: '学习中', value: 2 },
  { text: '已完成', value: 3 },
];

const sendNotice = async () => {
  try {
    if (noticeLoading.value) return;
    if (selectedStatus.value.length === 0) {
      showFailToast('请选择通知对象');
      return;
    }
    if (message.value.trim() === '') {
      showFailToast('请输入通知内容');
      return;
    }
    noticeLoading.value = true;
    const res = await sendNotification(planId, message.value, selectedStatus.value);
    if (res.code !== 0) throw new Error(res.msg);
    showToast('发送成功');     
    router.back();
  } catch (error) {
    showToast(error.message);
    console.log(error);
  } finally {
    noticeLoading.value = false;
  }
};
</script>

<template>
  <div class="flex flex-col overflow-hidden bg-white">
    <van-nav-bar title="发通知" left-arrow left-text="返回" @click-left="router.back()">
      <template #right>
        <span class="text-primary" @click="sendNotice">发送</span>
      </template>
    </van-nav-bar>

    <div @click="showBottom = true" class="ml-[32px] py-[32px] pr-[32px] flex items-center gap-[16px] border-b border-borderGrey">
      <img src="@/assets/profile/statistics/user.svg" alt="发送给" class="w-[24px]" />
      <span class="flex-1 text-[28px]">发送给</span>
      <div v-for="item in selectedStatus" :key="item" class="px-[16px] py-[4px] text-[28px] text-primary rounded-full bg-lightBlue leading-[44px]">
        {{ statusOptions.find((status) => status.value === item)?.text }}
      </div>
      <van-icon name="arrow" color="#878B95" />
    </div>

    <van-field v-model="message" rows="6" autosize type="textarea" maxlength="100" placeholder="请输入通知内容" show-word-limit />

    <van-popup v-model:show="showBottom" position="bottom" closeable :style="{ height: '30%' }">
      <div class="px-4 pt-10">
        <van-checkbox-group v-model="selectedStatus" direction="vertical">
          <van-checkbox v-for="option in statusOptions" :key="option.value" :name="option.value" class="mb-[28px] text-[28px]">
            {{ option.text }}
          </van-checkbox>
        </van-checkbox-group>
      </div>
    </van-popup>
  </div>
</template>
