<script setup>
import { ref } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import StatisticsChart from './StatisticsChart.vue';
import Segment from '@/components/Segment.vue';
import { getTrainingPlanDetail } from '@/api/profile';
import { getPieOptions } from '../utils/chart';

const router = useRouter();
const route = useRoute();

const { planId } = route.query;

const currentTab = ref(1);
const requestLoading = ref(false);
const userList = ref([]);
const pieOptions = ref(getPieOptions([], 0));

async function getPlanDetailInfo() {
  try {
    requestLoading.value = true;
    const res = await getTrainingPlanDetail(planId, currentTab.value);
    if (res.code !== 0) throw new Error(res.msg);
    userList.value = res.data.userList;
    const { notStartRate, notStartCount, startedRate, startedCount, endedRate, endedCount, totalCount } = res.data;
    const data = [
      { name: '未参加', value: notStartCount, rate: Math.round(notStartRate) },
      { name: '学习中', value: startedCount, rate: Math.round(startedRate) },
      { name: '已完成', value: endedCount, rate: Math.round(endedRate) },
    ];
    pieOptions.value = getPieOptions(data, totalCount);
  } catch (error) {
    console.log(error);
  } finally {
    requestLoading.value = false;
  }
}

getPlanDetailInfo();
</script>

<template>
  <div class="flex flex-col overflow-hidden" style="background: linear-gradient(180deg, #ccdafc 0%, #ffffff 20%)">
    <van-config-provider :theme-vars="{ navBarBackground: 'transparent' }">
      <van-nav-bar title="培训统计" left-arrow left-text="返回" @click-left="router.back()">
        <template #right>
          <div class="flex items-center gap-1" @click="router.push('/profile/statistic-notice?planId=' + planId)">
            <img src="@/assets/profile/statistics/notice.svg" alt="发通知" class="w-4" />
            <span>发通知</span>
          </div>
        </template>
      </van-nav-bar>
    </van-config-provider>

    <statistics-chart :options="pieOptions" />

    <Segment
      :rounded="true"
      class="mx-[32px] my-[16px]"
      :segments="[
        { value: 1, label: '未参加' },
        { value: 2, label: '学习中' },
        { value: 3, label: '已完成' },
      ]"
      v-model:activeKey="currentTab"
      @change="getPlanDetailInfo"
    />

    <van-skeleton v-if="requestLoading" title :row="8" />

    <van-empty v-if="!requestLoading && userList.length === 0" description="暂无学员" />

    <div v-if="!requestLoading && userList.length" class="flex-1 overflow-auto">
      <div v-for="user in userList" :key="user.userId" class="flex items-center gap-[16px] my-[16px] px-[32px]">
        <img class="w-[80px] h-[80px] rounded-full" :src="user.avatar" alt="" />
        <span class="flex-1 text-[28px]">{{ user.userName }}</span>
        <span class="text-[24px] text-desc">{{ user.deptName }}</span>
      </div>
    </div>
  </div>
</template>
