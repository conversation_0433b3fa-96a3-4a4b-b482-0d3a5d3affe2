<script setup>
import * as echarts from 'echarts';
import { onMounted, watch } from 'vue';

const props = defineProps({
  options: {
    type: Object,
    required: true,
  },
});

let chartInstance = null;

onMounted(() => {
  chartInstance = echarts.init(document.getElementById('statistics-chart'));
});

watch(
  () => props.options,
  () => {
    chartInstance.setOption(props.options);
  },
);
</script>

<template>
  <div id="statistics-chart" class="w-full h-[320px]"></div>
</template>
