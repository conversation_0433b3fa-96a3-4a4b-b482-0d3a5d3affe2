<script setup>
import { useRouter } from 'vue-router';
import { ref } from 'vue';
import { showToast } from 'vant';
import { getTrainingStatistics, getTrainingPlanProgress } from '@/api/profile';
import { getPlanTime, clearNaN } from '../utils';
import Segment from '@/components/Segment.vue';
import StatisticsChart from './StatisticsChart.vue';
import dayjs from 'dayjs';
import StatisticsTab from './StatisticsTab.vue';
import { getPieOptions } from '../utils/chart';

const router = useRouter();

const planLoading = ref(false);
const planStatus = ref(3);
const planList = ref([]);
const pagination = ref({ page: 0, total: 0 });

const statisticsLoading = ref(false);
const firm = ref('');
const placeId = ref('');
const center = ref('');
const statistics = ref();
const pieOptions = ref(getPieOptions([], 0));

const showPicker = ref(false);
const currentDate = ref([dayjs().year(), dayjs().month() + 1]);
const lastDate = ref([dayjs().year(), dayjs().month() + 1]);

async function getTrainingStatisticsInfo() {
  try {
    statisticsLoading.value = true;
    const [year, month] = currentDate.value;
    const { startTime, endTime } = getPlanTime(
      'month',
      dayjs()
        .year(year)
        .month(month - 1),
    );
    const res = await getTrainingStatistics(startTime, endTime, placeId.value);
    if (res.code !== 0) throw new Error(res.msg);
    statistics.value = res.data;
    const { notStartRate, notStartCount, startedRate, startedCount, endedRate, endedCount, totalCount } = statistics.value;
    const data = [
      { name: '未开始', value: notStartCount, rate: Math.round(clearNaN(notStartRate)) },
      { name: '未完成', value: startedCount, rate: Math.round(clearNaN(startedRate)) },
      { name: '已完成', value: endedCount, rate: Math.round(clearNaN(endedRate)) },
    ];
    pieOptions.value = getPieOptions(data, totalCount);
  } catch (error) {
    showToast(error.message);
  } finally {
    statisticsLoading.value = false;
  }
}

async function getTrainingPlanProgressInfo() {
  try {
    planLoading.value = true;
    pagination.value.page += 1;
    const [year, month] = currentDate.value;
    const { startTime, endTime } = getPlanTime(
      'month',
      dayjs()
        .year(year)
        .month(month - 1),
    );
    const res = await getTrainingPlanProgress(startTime, endTime, placeId.value, planStatus.value, pagination.value.page, 10);
    if (res.code !== 0) throw new Error(res.msg);
    planList.value = [...planList.value, ...res.data.planVoList];
    pagination.value.total = res.data.total;
  } catch (error) {
    showToast(error.message);
  } finally {
    planLoading.value = false;
  }
}

function handleTabChange(_firm, _placeId, _center) {
  firm.value = _firm;
  placeId.value = _placeId;
  center.value = _center;
  currentDate.value = [dayjs().year(), dayjs().month() + 1];
  lastDate.value = [dayjs().year(), dayjs().month() + 1];
  planStatus.value = 3;
  pagination.value.page = 0;
  planList.value = [];  
  getTrainingStatisticsInfo();
  getTrainingPlanProgressInfo();
}
</script>

<template>
  <div class="flex flex-col overflow-hidden bg-page">
    <van-nav-bar title="培训统计" left-arrow left-text="返回" @click-left="router.back()" />

    <statistics-tab @change="handleTabChange" />

    <div class="flex-1 flex flex-col overflow-hidden p-[32px] pb-0">
      <div class="px-[24px] py-[32px] bg-white rounded-[16px]">
        <div class="flex items-center justify-between">
          <span class="font-medium text-[32px]">{{ center }}培训概况</span>
          <span @click="showPicker = true" class="text-[28px] text-primary"
            >{{ currentDate[0] }}年{{ Number(currentDate[1]) }}月<van-icon name="arrow-down"
          /></span>
        </div>

        <statistics-chart :options="pieOptions" />

        <div class="flex gap-[32px]">
          <div class="flex-1 p-[16px] rounded-[12px] bg-gradient-to-b from-[#DCF6F0] to-[#F5F8FF]">
            <div class="flex items-center justify-between text-[28px]">
              <span>平均合格率</span>
              <img src="@/assets/profile/statistics/rank.svg" alt="箭头" class="w-[40px]" />
            </div>
            <div class="mt-[16px] font-medium">
              <span class="text-[40px]">{{ Math.round(statistics?.averagePassRate) || 0 }}</span>
              <span class="text-[24px]">%</span>
            </div>
          </div>

          <div
            @click="router.push(`/profile/statistic-rank?year=${currentDate[0]}&month=${currentDate[1]}&placeId=${placeId}&firmName=${firm}`)"
            class="flex-1 p-[16px] rounded-[12px] bg-gradient-to-b from-[#D9E4FD] to-[#F5F8FF]"
          >
            <div class="flex items-center justify-between text-[28px]">
              <span>当前部门排名</span>
              <img src="@/assets/profile/statistics/deptRank.svg" alt="箭头" class="w-5" />
            </div>
            <div class="mt-2 flex items-center font-medium">
              <span class="text-[40px]">{{ (statistics?.placeRankingVos.find((item) => item.placeId === placeId)?.num || 0) + 1 }}</span>
              <span class="flex-1 text-[24px]">名</span>
              <van-icon name="arrow" color="#878B95" style="font-size: 12px" />
            </div>
          </div>
        </div>
      </div>

      <segment
        class="mt-[32px] w-full"
        :rounded="true"
        :segments="[
          { label: '未开始', value: 3 },
          { label: '未完成', value: 1 },
          { label: '已完成', value: 2 },
        ]"
        v-model:activeKey="planStatus"
        @change="
          () => {
            pagination.page = 0;
            planList = [];
            getTrainingPlanProgressInfo();
          }
        "
      />

      <div class="flex-1 overflow-auto mt-[16px]">
        <van-list
          v-model:loading="planLoading"
          :finished="planList.length >= pagination.total"
          finished-text="没有更多了"
          @load="getTrainingPlanProgressInfo"
        >
          <div
            @click="router.push(`/profile/statistic-course?planId=${item.planId}`)"
            class="bg-white rounded-[16px] p-[24px] mt-[16px]"
            v-for="item in planList"
            :key="item.planId"
          >
            <div class="flex items-center">
              <div class="flex-1 mr-[32px]">
                <div class="text-[28px]">{{ item.planName }}</div>
                <div class="flex items-center gap-[8px] mt-[16px] text-[24px] text-desc">
                  <img src="@/assets/profile/statistics/user.svg" alt="学习人数" class="h-[32px]" />
                  <span>学习人数</span>
                  <span>{{ item.passCount }}/{{ item.totalCount }}</span>
                </div>
              </div>
              <div class="relative mr-[16px]">
                <div class="absolute inset-0 rounded-full bg-gradient-to-r from-[#2c7dff] via-[#89a6ea] to-[#2c67ec]"></div>
                <div
                  class="relative flex flex-col items-center justify-center w-[100px] h-[100px] rounded-full bg-white m-[16px] text-primary font-medium"
                >
                  <span class="text-[20px]">{{ Math.round(item.passRate) }}%</span>
                  <span class="text-[18px]">完成</span>
                </div>
              </div>
              <van-icon name="arrow" color="#878B95" style="font-size: 12px" />
            </div>
          </div>
        </van-list>
      </div>
    </div>

    <van-popup v-model:show="showPicker" position="bottom" :style="{ height: '40%' }">
      <van-date-picker
        v-model="currentDate"
        title="时间选择"
        :min-date="new Date(2021, 0, 1)"
        :max-date="new Date(2030, 11, 1)"
        :columns-type="['year', 'month']"
        :formatter="
          (type, option) => {
            if (type === 'year') {
              option.text += '年';
            }
            if (type === 'month') {
              option.text = String(Number(option.text)) + '月';
            }
            return option;
          }
        "
        @confirm="
          () => {
            planStatus = 3;
            pagination.page = 0;
            planList = [];
            getTrainingStatisticsInfo();
            getTrainingPlanProgressInfo();
            showPicker = false;
          }
        "
        @cancel="
          () => {
            currentDate = lastDate;
            showPicker = false;
          }
        "
      />
    </van-popup>
  </div>
</template>
