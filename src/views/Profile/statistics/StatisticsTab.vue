<script setup>
import Tabs from '../../../components/Tabs.vue';
import { getCategoryList } from '@/api/home';
import { ref, reactive, computed } from 'vue';
import { showToast } from 'vant';

const emit = defineEmits(['change']);

const loading = ref(false);
const activeTab = ref('');
const firm = ref('');
const companyList = ref({});

const placeOptions = computed(() => companyList.value[firm.value]?.map((place) => ({ label: place.center, value: place.id })) || []);

const showPicker = ref(false);

async function getCompanyListInfo() {
  try {
    loading.value = true;
    const res = await getCategoryList();
    if (res.code !== 0) throw new Error(res.msg);
    companyList.value = res.data;
    firm.value = Object.keys(companyList.value)[0] || '';
    const { id } = companyList.value[firm.value][0];
    activeTab.value = id;
  } catch (error) {
    showToast(error.message);
  } finally {
    loading.value = false;
  }
}

function handleChangeDept(_place) {
  const { label, value } = _place;
  activeTab.value = value;
  emit('change', firm.value, value, label);
}

function handleChangeFirm(_firm) {
  firm.value = _firm;
  const { id, center } = companyList.value[firm.value][0];
  activeTab.value = id;
  emit('change', firm.value, id, center);
}

getCompanyListInfo();
</script>

<template>
  <div class="flex items-center bg-white">
    <tabs :tabs="placeOptions" v-model="activeTab" class="flex-1" @change="handleChangeDept" />
    <div @click="showPicker = true" class="px-[10px] text-[28px] text-desc"><van-icon name="filter-o" />切换</div>
  </div>

  <van-popup v-model:show="showPicker" position="bottom" round class="p-[32px] h-3/5 overflow-auto">
    <div class="mb-[40px] text-[32px] text-center">切换</div>

    <div>
      <div class="my-[8px] text-[28px] font-medium leading-[44px]">公司</div>
      <div class="flex gap-[24px] w-full flex-wrap">
        <div
          v-for="item in Object.keys(companyList)"
          :key="item"
          class="px-[20px] py-[12px] rounded-[12px] text-[28px]"
          :class="firm === item ? 'text-primary bg-lightBlue' : 'text-desc bg-[#F2F3F5]'"
          @click="handleChangeFirm(item)"
        >
          {{ item }}
        </div>
      </div>
    </div>

    <div class="mt-[24px]">
      <div class="my-[8px] text-[28px] font-medium leading-[44px]">楼宇</div>
      <div class="flex gap-[24px] w-full flex-wrap">
        <div
          v-for="item in placeOptions"
          :key="item.value"
          class="px-[20px] py-[12px] rounded-[12px] text-[28px]"
          :class="activeTab === item.value ? 'text-primary bg-lightBlue' : 'text-desc bg-[#F2F3F5]'"
          @click="handleChangeDept(item)"
        >
          {{ item.label }}
        </div>
      </div>
    </div>
  </van-popup>
</template>
