<script setup>
import { ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import Segment from '@/components/Segment.vue';
import { getRankFontColor, getDurationStr, getPlanTime } from '../utils';
import { showToast } from 'vant';
import { getTrainingStatistics } from '@/api/profile';
import dayjs from 'dayjs';

const router = useRouter();
const route = useRoute();

const { year, month, placeId, firmName } = route.query;

const currentTab = ref('month');
const showMonthPicker = ref(false);
const showYearPicker = ref(false);
const currentMonth = ref([year, month]);
const lastMonth = ref([year, month]);
const currentYear = ref([year]);
const lastYear = ref([year]);

const loading = ref(false);
const rankList = ref([]);

async function getTrainingStatisticsInfo() {
  try {
    loading.value = true;
    const date =
      currentTab.value === 'month'
        ? dayjs()
            .year(currentMonth.value[0])
            .month(currentMonth.value[1] - 1)
        : dayjs().year(currentYear.value[0]);
    const { startTime, endTime } = getPlanTime(currentTab.value, date);
    const res = await getTrainingStatistics(startTime, endTime, placeId);
    if (res.code !== 0) throw new Error(res.msg);
    rankList.value = res.data.placeRankingVos;
  } catch (error) {
    showToast(error.message);
  } finally {
    loading.value = false;
  }
}

getTrainingStatisticsInfo();
</script>

<template>
  <div class="flex flex-col overflow-hidden">
    <van-nav-bar :title="firmName" left-arrow left-text="返回" @click-left="router.back()">
      <template #right>
        <div v-show="currentTab === 'month'" @click="showMonthPicker = true">{{ currentMonth[0] }}年{{ Number(currentMonth[1]) }}月</div>
        <div v-show="currentTab === 'year'" @click="showYearPicker = true">{{ currentYear[0] }}年</div>
      </template>
    </van-nav-bar>

    <segment
      class="mx-[32px] mt-[32px] mb-[16px]"
      :rounded="true"
      :segments="[
        { label: '月', value: 'month' },
        { label: '年', value: 'year' },
      ]"
      v-model:activeKey="currentTab"
      @change="getTrainingStatisticsInfo"
    />

    <van-skeleton v-if="loading" title :row="8" />

    <van-empty v-if="!loading && rankList.length === 0" />

    <div v-if="!loading && rankList.length > 0" class="flex-1 overflow-auto px-[32px]">
      <div v-for="dept in rankList" :key="dept.placeId" class="flex items-center justify-between gap-[16px] py-[12px] mt-[8px]">
        <span class="px-[8px] w-[80px] font-youShe text-[32px] van-ellipsis" :style="{ color: getRankFontColor(dept.num) }">{{ dept.num + 1 }}</span>
        <span class="flex-1 text-[32px]">{{ dept.centerName }}</span>
        <span class="text-[24px] text-descColor">{{ Math.round(dept.averagePassRate) }}%</span>
      </div>
    </div>

    <van-popup v-model:show="showMonthPicker" position="bottom" :style="{ height: '40%' }">
      <van-date-picker
        v-model="currentMonth"
        title="时间选择"
        :min-date="new Date(2021, 0, 1)"
        :max-date="new Date(2030, 11, 1)"
        :columns-type="['year', 'month']"
        :formatter="
          (type, option) => {
            if (type === 'year') {
              option.text += '年';
            }
            if (type === 'month') {
              option.text = String(Number(option.text)) + '月';
            }
            return option;
          }
        "
        @confirm="
          () => {
            getTrainingStatisticsInfo();
            showMonthPicker = false;
          }
        "
        @cancel="
          () => {
            currentMonth = lastMonth;
            showMonthPicker = false;
          }
        "
      />
    </van-popup>

    <van-popup v-model:show="showYearPicker" position="bottom" :style="{ height: '40%' }">
      <van-date-picker
        v-model="currentYear"
        title="时间选择"
        :min-date="new Date(2021, 0, 1)"
        :max-date="new Date(2030, 11, 1)"
        :columns-type="['year']"
        :formatter="
          (type, option) => {
            if (type === 'year') {
              option.text += '年';
            }
            return option;
          }
        "
        @confirm="
          () => {
            getTrainingStatisticsInfo();
            showYearPicker = false;
          }
        "
        @cancel="
          () => {
            currentYear = lastYear;
            showYearPicker = false;
          }
        "
      />
    </van-popup>
  </div>
</template>
