<script setup>
import { HomeApi } from '@/api';
import Toast from '@/components/Toast.vue';
import VantTabBar from '@/components/VantTabBar.vue';
import { useRedDotStore } from '@/store';
import { backClient } from '@/utils';
import { onMounted, onUnmounted } from 'vue';

const redDotStore = useRedDotStore();

const getRedDot = async () => {
  try {
    const res = await HomeApi.getRedDot();
    if (res.code != 0) throw new Error(res.msg);
    const { planNum, testNum } = res.data;
    redDotStore.setPlanNum(planNum);
    redDotStore.setTestNum(testNum);
  } catch (e) {
    console.log('获取红点失败', e);
    Toast(e.message);
  }
};

getRedDot();

onMounted(() => {
  window.addEventListener('popstate', backClient);
});

onUnmounted(() => {
  window.removeEventListener('popstate', backClient);
});
</script>

<template>
  <div class="flex flex-col h-full overflow-hidden">
    <router-view class="flex-1" />
    <div class="h-[90px]"></div>
    <vant-tab-bar></vant-tab-bar>
  </div>
</template>

<style scoped lang="scss"></style>
