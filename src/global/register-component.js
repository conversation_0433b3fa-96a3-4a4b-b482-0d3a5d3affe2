import GlobalTabBar from '@/components/GlobalTabBar.vue';
import Loading from '@/components/Loading.vue';

// vant组件
import {
  NavBar,
  Tabbar,
  Empty,
  List,
  Collapse,
  CollapseItem,
  Row,
  Col,
  Button,
  DropdownMenu,
  DropdownItem,
  ConfigProvider,
  Popup,
  Icon,
  DatePicker,
  Picker,
  Sticky
} from 'vant';

const components = [
  NavBar,
  Tabbar,
  Empty,
  List,
  GlobalTabBar,
  Loading,
  Collapse,
  CollapseItem,
  Row,
  Col,
  Button,
  DropdownMenu,
  DropdownItem,
  ConfigProvider,
  Popup,
  Icon,
  DatePicker,
  Picker,
  Sticky
];

export default function (app) {
  for (const component of components) {
    app.use(component);
  }
}
