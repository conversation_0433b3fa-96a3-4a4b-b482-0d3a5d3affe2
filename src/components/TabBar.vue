<template>
  <div class="flex-footer">
    <div class="footer">
      <div class="footer-box">
        <Item class="item" :txt="item.txt" :page="item.page" @change="getVal" v-for="(item, index) in tabbarDes" :key="index" :num="index">
          <template v-slot:img>
            <img :src="item.img" />
          </template>
          <template v-slot:onImg>
            <img :src="item.onImg" />
          </template>
        </Item>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref } from 'vue';

import { useBaseStore } from '@/store/base';
import Item from './TabbarItem.vue';

const baseStore = useBaseStore();

// const currentTabBarIndex = baseStore.currentTabBarIndex

const tabbarDes = ref([
  {
    img: require('@/assets/base/home.png'),
    onImg: require('@/assets/base/home_active.png'),
    txt: '主页',
    page: '',
  },
  {
    img: require('@/assets/base/course.png'),
    onImg: require('@/assets/base/course_active.png'),
    txt: '课表',
    page: 'course',
  },
]);

const getVal = (value) => {
  baseStore.changeVal(value);
};
</script>

<style lang="scss" scoped>
/* 底部固定导航栏 */
.footer {
  width: 750px;
  height: 98px;
  background: #ffffff;
  position: fixed;
  left: 0;
  bottom: 0;
  border-top: 1px solid #e5e5e5;

  .footer-box {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
  }
}
</style>
