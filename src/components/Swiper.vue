<template>
  <div class="swiper">
    <div class="region">
      <div ref="sliderListRef" class="sliderList" @touchstart="touchStart" @touchmove="touchMove" @touchend="touchEnd">
        <slot></slot>
      </div>
    </div>
    <ul class="indicator" v-if="sliderDomsLen > 1">
      <li v-for="n in sliderDomsLen" :key="n" :class="{ currentLi: currentIndex === n }"></li>
    </ul>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
const props = defineProps({
  imgList: Array,
  // 间隔
  interval: {
    type: Number,
    default: 4000,
  },
  // 滑动持续事件
  animDuration: {
    type: Number,
    default: 500,
  },
  // 移动比例
  moveRatio: {
    type: Number,
    default: 0.25,
  },
});

let sliderListStyle = ref({}); // 滑动区域的样式
let sliderWidth = 0;
let currentIndex = ref(1); // 当前滑块的索引
let playTimer = null;
let startX = 0;
let moveX = 0;
let isSliding = false; // 是否正在滑动
const sliderListRef = ref(null);
onMounted(() => {
  handleDom();
  startTimer();
});

let sliderDomsLen = ref(0); // 传来的slider个数

// 操作dom元素
const handleDom = () => {
  const sliderDoms = sliderListRef.value.children;
  sliderDomsLen.value = sliderDoms.length;

  if (sliderDomsLen.value > 1) {
    const firstEl = sliderDoms[0].cloneNode(true);
    const lastEl = sliderDoms[sliderDomsLen.value - 1].cloneNode(true);
    sliderListRef.value.insertBefore(lastEl, sliderDoms[0]);
    sliderListRef.value.appendChild(firstEl);

    sliderListStyle.value = sliderListRef.value.style;
    sliderWidth = sliderListRef.value.offsetWidth;
    setTransform(sliderWidth);
  }
};

// 开启定时器
const startTimer = () => {
  if (playTimer !== null) return;
  playTimer = setInterval(() => {
    isSliding = true;
    // 1. 开启动画
    sliderListStyle.value.transition = `all ${props.animDuration}ms`;
    // 2. 滚动到想要位置
    setTransform((currentIndex.value + 1) * sliderWidth);

    // 3. 判断滚动后下一次的位置
    window.setTimeout(() => {
      isSliding = false;
      sliderListStyle.value.transition = 'none';

      if (currentIndex.value >= sliderDomsLen.value) {
        currentIndex.value = 1;
        setTransform(currentIndex.value * sliderWidth);
      } else {
        currentIndex.value = currentIndex.value + 1;
      }
    }, props.animDuration);
  }, props.interval);
};

// 关闭定时器
const stopTimer = () => {
  clearInterval(playTimer);
  playTimer = null;
};

// 设置滚动的位置
const setTransform = (position) => {
  sliderListStyle.value.transform = `translateX(-${position}px)`;
};

// 用户滑动事件处理
const touchStart = (e) => {
  if (isSliding) return;
  stopTimer();
  // 3.保存开始滚动的位置
  startX = Math.round(e.touches[0].pageX);
};
const touchMove = (e) => {
  if (isSliding) return;
  const endX = Math.round(e.touches[0].pageX);
  moveX = startX - endX;

  // 滑动跟手
  sliderListStyle.value.transform = `translateX(-${currentIndex.value * sliderWidth + moveX}px)`;
};
const touchEnd = (e) => {
  if (isSliding) return;

  isSliding = true;
  if (Math.abs(moveX) > sliderWidth * props.moveRatio) {
    // 有效的滑动
    moveX > 0 ? currentIndex.value++ : currentIndex.value--;
  }

  // 滑动后过度动画
  sliderListStyle.value.transition = `all 0.3s`;
  setTransform(currentIndex.value * sliderWidth);
  setTimeout(() => {
    sliderListStyle.value.transition = 'none';
    if (currentIndex.value > sliderDomsLen.value) {
      currentIndex.value = 1;
      setTransform(currentIndex.value * sliderWidth);
    } else if (currentIndex.value < 1) {
      currentIndex.value = sliderDomsLen.value;
      setTransform(currentIndex.value * sliderWidth);
    }
    // 滑动结束再次开启轮播
    isSliding = false;
    startTimer();
  }, 300);
};
</script>

<style lang="scss" scoped>
.swiper {
  // width: 100%;

  .region {
    overflow: hidden;
    border-radius: 16px;
    .sliderList {
      display: flex;
    }
  }
  .indicator {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    li {
      display: inline-block;
      list-style-type: none;
      height: 10px;
      width: 10px;
      margin: 6px;
      border-radius: 50%;
      background: #dbdee9;
    }
    .currentLi {
      background: #a9b3c4;
    }

    .active {
      background-color: rgb(169, 179, 196);
    }
  }
}
</style>
