<template>
  <transition name="fade">
    <div class="fixed-mark" v-if="isShow" id="a_l_e_n_g_loading">
      <img src="https://mobile.ddbes.com/static-lib/icon/loading.gif" />
    </div>
  </transition>
</template>
<script setup>
import { ref } from 'vue';

defineProps({
  isShow: Boolean,
});
</script>

<style scoped>
.fixed-mark {
  background-color: rgba(0, 0, 0, 0.6);
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  z-index: 999;
}

.fixed-mark img {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -80%);
  width: 200px;
  height: 200px;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s;
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
}
</style>
