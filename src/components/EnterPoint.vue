<script setup>
import EnterPointIcon from '@/assets/base/enter.png';

defineProps({
  title: {
    type: String,
    required: true,
  },
  icon: {
    type: String,
    default: EnterPointIcon,
  },
});
</script>

<template>
  <div class="flex justify-between items-center gap-[4px]">
    <div class="text-[24px] text-desc">{{ title }}</div>
    <img :src="icon" class="w-[40px] h-[40px]" alt="enter-point" />
  </div>
</template>
