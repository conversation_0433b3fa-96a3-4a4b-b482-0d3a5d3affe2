<template>
  <div class="bar">
    <div class="title">
      <span class="line"></span>
      <span>{{ title }}</span>
      <span v-if="messageNum">({{ messageNum }})</span>
    </div>
    <div class="right">
      <slot></slot>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
defineProps({
  title: String,
  messageNum: Number,
});
</script>

<style lang="scss" scoped>
.bar {
  height: 42px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .title {
    font-size: 30px;
    font-family:
      PingFangSC-Medium,
      PingFang SC;
    font-weight: 500;
    color: #23252a;
    display: flex;
    .line {
      margin-right: 17px;
      display: inline-block;
      width: 6px;
      height: 30px;
      background-color: rgb(50, 111, 255);
    }
  }
  .right {
    font-size: 26px;
    font-family:
      PingFangSC-Regular,
      PingFang SC;
    font-weight: 400;
    color: #878b95;
  }
}
</style>
