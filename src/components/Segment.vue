<script setup>
import { ref, computed, withDefaults, nextTick, onMounted } from 'vue';

const props = defineProps({
  segments: {
    required: true,
    default: () => [],
  },
  theme: {
    type: String,
    default: 'white',
  },
  activeKey: {
    type: [Number, String],
  },
  rounded: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['update:activeKey', 'change']);

const themeColor = computed(() => ({
  background: props.theme === 'white' ? '#eeeeee' : 'white',
  itemBackground: props.theme === 'white' ? 'white' : 'rgba(44, 103, 236, 0.1)',
  border: props.theme === 'white' ? 'none' : '1px solid #E7E7E7',
  wrapperBorderRadius: props.rounded ? '16px' : '10px',
  activeBorderRadius: props.rounded ? '16px' : '8px',
}));

const segmentRefs = ref([]);

const setSegmentRef = (el, index) => {
  if (el) {
    segmentRefs.value[index] = el;
  }
};

const handleSegmentClick = (value) => {
  if (value === props.activeKey) return;
  emit('update:activeKey', value);
  emit('change', value);
};

const getActiveStyle = computed(() => {
  if (!props.segments.length) return { opacity: 0 };

  const currentIndex = props.segments.findIndex((segment) => segment.value === props.activeKey);
  if (currentIndex === -1) return { opacity: 0 };

  const currentSegment = segmentRefs.value[currentIndex];
  if (!currentSegment) return { opacity: 0 };

  return {
    transform: `translateX(${currentSegment.offsetLeft}px)`,
    width: `${currentSegment.offsetWidth}px`,
    opacity: 1,
  };
});

/**
 * 监听可见性，避免组件在 v-show 为 false 时，无法获取到正确的 offsetLeft
 */

const containerRef = ref(null);

onMounted(() => {
  const observer = new IntersectionObserver((entries) => {
    if (entries[0].isIntersecting) {
      nextTick(() => {
        segmentRefs.value = [...segmentRefs.value];
      });
    }
  });

  if (containerRef.value) {
    observer.observe(containerRef.value);
  }
});
</script>

<template>
  <div class="segment-container" ref="containerRef">
    <div class="segment-background">
      <div class="segment-active-background" :style="getActiveStyle" />
      <div class="segment-items">
        <div
          v-for="(segment, index) in segments"
          :key="segment.value"
          :ref="(el) => setSegmentRef(el, index)"
          class="segment-item"
          :class="{ 'segment-item-active': activeKey === segment.value }"
          @click="handleSegmentClick(segment.value)"
        >
          {{ segment.label }}
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.segment-container {
  display: inline-block;
  min-width: 148px;
}

.segment-background {
  position: relative;
  background-color: v-bind('themeColor.background');
  border: v-bind('themeColor.border');
  border-radius: v-bind('themeColor.wrapperBorderRadius');
  padding: 8px;
}

.segment-active-background {
  position: absolute;
  top: 8px;
  left: 8px;
  height: calc(100% - 16px);
  background-color: v-bind('themeColor.itemBackground');
  border-radius: v-bind('themeColor.activeBorderRadius');
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.segment-items {
  position: relative;
  display: flex;
}

.segment-item {
  flex: 1;
  position: relative;
  padding: 8px 0;
  font-size: 28px;
  color: rgba(0, 0, 0, 0.4);
  cursor: pointer;
  border-radius: 8px;
  transition: color 0.3s ease;
  text-align: center;
  user-select: none;
}

.segment-item-active {
  color: #2c67ec;
  font-weight: 500;
}
</style>
