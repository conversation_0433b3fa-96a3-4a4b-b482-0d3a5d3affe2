<script setup>
import { ref, watch } from 'vue';

const props = defineProps({
  mode: {
    type: String,
    default: 'write', // write | read
  },
  question: {
    type: Object,
    required: true,
  },
  isRight: {
    type: Boolean,
    default: false,
  },
  myAnswer: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['change']);

const JudgeOptions = [
  {
    id: 'T',
    tag: 'A',
    optionText: '正确',
  },
  {
    id: 'F',
    tag: 'B',
    optionText: '错误',
  },
];

const activeOpt = ref('');

if (props.mode === 'read') {
  activeOpt.value = JSON.parse(JSON.stringify(props.question.answer));
} else if (props.mode === 'write') {
  if (props.question.myAnswer) {
    activeOpt.value = JSON.parse(JSON.stringify(props.question.myAnswer));
  }
}

function getOptions() {
  const options = props.question.questionOptions;
  if (options === undefined) return [];
  return options.length > 0 ? options : JudgeOptions;
}

// 是否选中
function isChecked(id) {
  if (props.mode === 'read' || activeOpt.value === undefined) return false;
  return activeOpt.value.includes(id);
}

/** write */

function handleChange(id) {
  if (props.mode === 'read') return;
  const type = props.question.type;
  if (type === 'SINGLE' || type === 'T_OR_F') {
    activeOpt.value = id;
  } else if (type === 'MULTI') {
    const arr = activeOpt.value === '' ? [] : activeOpt.value.split(',');
    if (arr.includes(id)) {
      arr.splice(arr.indexOf(id), 1);
    } else {
      arr.push(id);
    }
    activeOpt.value = arr.join(',');
  }
  emit('change', activeOpt.value);
}

/** read */

// 是否为正确选项
function isRight(id) {
  return props.mode === 'read' && props.isRight && props.myAnswer.includes(id);
}

// 是否为错误选项
function isWrong(id) {
  return props.mode === 'read' && !props.isRight && props.myAnswer.includes(id);
}
</script>

<template>
  <div class="flex flex-col">
    <div
      v-for="item in getOptions()"
      :key="item.tag"
      class="py-[18px] pl-[80px] pr-[30px] flex items-center rounded-[8px]"
      :class="{ 'bg-bgBlue': isChecked(item.id) }"
      @click="handleChange(item.id)"
    >
      <van-row class="w-full">
        <van-col span="2">
          <div
            class="flex justify-center items-center border w-[45px] h-[45px]"
            :class="`${isChecked(item.id) ? 'border-blue' : 'border-border'} ${props.question.type === 'MULTI' ? 'rounded-[8px]' : 'rounded-[50%]'}`"
          >
            <div v-if="isRight(item.id)">✅</div>
            <div v-else-if="isWrong(item.id)">❌</div>
            <span v-else class="text-[30px] font-font font-normal" :class="isChecked(item.id) ? 'text-blue' : 'text-simple'">{{ item.tag }} </span>
          </div>
        </van-col>
        <van-col span="22" class="pl-[15px]"
          ><span class="text-[30px] font-font font-normal" :class="isChecked(item.id) ? 'text-blue' : 'text-simple'">{{ item.optionText }} </span>
        </van-col>
      </van-row>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
