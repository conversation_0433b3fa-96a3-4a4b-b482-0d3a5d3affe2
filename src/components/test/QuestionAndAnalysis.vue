<script setup>
import Tag from '@/components/Tag.vue';
import RadioOptions from '@/components/test/RadioOptions.vue';

const props = defineProps({
  mode: {
    type: String,
    default: 'write', // write | read
  },
  index: {
    type: Number,
    default: 0,
  },
  question: {
    type: Object,
    default: () => ({}),
  },
  isRight: {
    type: Boolean,
    default: false,
  },
  myAnswer: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['change']);

function handleAnswerChange(idStr) {
  emits('change', props.question.id, props.question, idStr);
}

function getAnswerTag() {
  const { type, questionOptions, answer } = props.question;
  let rightStr = '';
  switch (type) {
    case 'SINGLE':
      var rightOption = questionOptions.find((item) => item.id === answer);
      rightOption && (rightStr = rightOption.tag);
      break;
    case 'MULTI':
      var rightOptions = questionOptions.filter((item) => answer.includes(item.id)).map((item) => item.tag);
      rightStr = rightOptions.join(',');
      break;
    case 'T_OR_F':
      rightStr = answer === 'T' ? 'A' : 'B';
      break;
  }
  return rightStr;
}

function getQuestionType() {
  const { type } = props.question;
  switch (type) {
    case 'SINGLE':
      return '单选题';
    case 'MULTI':
      return '多选题';
    case 'T_OR_F':
      return '判断题';
  }
}
</script>

<template>
  <div>
    <div class="bg-white py-[30px]">
      <!-- 题干 -->
      <div class="flex gap-[10px] px-[30px] pb-[20px]">
        <div class="w-[50px] text-right">{{ props.index + 1 }}.</div>
        <div class="flex-1">
          <tag class="inline-block" type="primary" :message="getQuestionType()" />
          <span class="ml-[5px] text-[30px] font-font font-normal text-simple">
            {{ props.question.stem }}
          </span>
        </div>
      </div>
      <!-- 选项 -->
      <radio-options
        :key="props.question.id"
        :question="props.question"
        :mode="props.mode"
        @change="handleAnswerChange"
        :isRight="props.isRight"
        :myAnswer="props.myAnswer"
      />
    </div>
    <div v-if="props.mode && props.mode === 'read'" class="pl-[80px] pt-[32px] pr-[44px] pb-[30px]">
      <div class="flex items-center mb-[30px]">
        <!--  答案 -->
        <div class="text-[35px] font-font font-normal">
          <span class="text-green" v-show="props.isRight">正确答案 {{ getAnswerTag() }}</span>
          <span class="text-orange" v-show="!props.isRight">正确答案 {{ getAnswerTag() }}</span>
        </div>
        <!-- 是否修改过答案 -->
        <!-- <tag message="答案已修改" type="danger" /> -->
      </div>
      <!--  解析 -->
      <div class="flex text-[30px] font-font">
        <div class="w-[100px] font-medium text-titleColor">解析:</div>
        <div class="font-normal text-simple">
          {{ props.question.analysis || '暂无解析' }}
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
