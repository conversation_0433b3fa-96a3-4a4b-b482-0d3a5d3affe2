<script setup>
const props = defineProps({
  index: {
    type: Number,
    default: 0,
  },
  length: {
    type: Number,
    default: 0,
  },
});

const emit = defineEmits(['handleChangeToNext', 'handleChangeToLast', 'showPopup']);
</script>

<template>
  <div class="flex items-center h-[130px] w-full bg-white">
    <!--  导航：第一题且不是最后一题  -->
    <div v-show="props.index === 0 && props.index !== props.length - 1" class="flex w-full h-[80px]">
      <div @click="emit('handleChangeToNext')" class="w-1/2 tab-item tab-item-border">下一题</div>
      <div @click="emit('showPopup', true)" class="w-1/2 tab-item">全部题目</div>
    </div>
    <!--  导航：中间题目  -->
    <div v-show="props.index > 0 && props.index < props.length - 1" class="flex w-full h-[80px]">
      <div @click="emit('handleChangeToLast')" class="w-1/3 tab-item tab-item-border">上一题</div>
      <div @click="emit('handleChangeToNext')" class="w-1/3 tab-item tab-item-border">下一题</div>
      <div @click="emit('showPopup', true)" class="w-1/3 tab-item">全部题目</div>
    </div>
    <!--  导航：最后一题且不是第一题  -->
    <div v-show="props.index === props.length - 1 && props.index !== 0" class="flex w-full h-[80px]">
      <div @click="emit('handleChangeToLast')" class="w-1/2 tab-item tab-item-border">上一题</div>
      <div @click="emit('showPopup', true)" class="w-1/2 tab-item">全部题目</div>
    </div>
    <!--  导航：第一题且最后一题  -->
    <div v-show="props.index === props.length - 1 && props.index === 0" class="flex w-full h-[80px]">
      <div @click="emit('showPopup', true)" class="w-full tab-item">全部题目</div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.tab-item {
  @apply h-full text-center leading-[80px];
}

.tab-item-border {
  @apply border-r border-border;
}
</style>
