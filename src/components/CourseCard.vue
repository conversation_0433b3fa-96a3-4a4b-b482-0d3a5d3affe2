<script setup>
import CategoryIcon from '@/assets/base/course-card/category.svg';
import UserIcon from '@/assets/base/course-card/user.svg';
import TextEllipsis from '@/components/TextEllipsis.vue';

const props = defineProps({
  showThumbnail: {
    type: Boolean,
    default: false,
  },
  thumb: {
    type: String,
    default: '',
  },
  title: {
    type: String,
    default: '消防设施操作员必考+选考理论实践',
  },
  category: {
    type: String,
    default: '运维管理',
  },
  teacher: {
    type: String,
    default: '姜老师',
  },
  mode: {
    type: String,
    default: 'vertical', // vertical, horizontal
  },
});
</script>

<template>
  <div class="flex" :class="mode === 'vertical' ? 'flex-col w-[270px]' : 'flex-row'">
    <div class="flex-shrink-0 relative rounded-[16px] overflow-hidden">
      <img class="w-[270px] h-[180px]" :src="thumb" alt="课程封面" />
      <div
        v-if="showThumbnail"
        class="absolute bottom-0 flex items-center justify-end pr-[10px] w-full h-[50px] text-[24px] text-white bg-gradient-to-b from-transparent to-black opacity-60"
      >
        <slot name="thumb" />
      </div>
    </div>
    <div class="flex-1" :class="mode === 'vertical' ? 'mt-[16px]' : 'ml-[16px]'">
      <text-ellipsis class="text-[26px]" :clamp="2">{{ title }}</text-ellipsis>
      <div class="mt-[12px] flex items-center">
        <img class="w-[32px] h-[32px]" :src="CategoryIcon" alt="" />
        <text-ellipsis class="ml-[8px] text-[24px] text-desc">{{ category }}</text-ellipsis>
      </div>
      <div class="mt-[12px] flex items-center">
        <img class="w-[32px] h-[32px]" :src="UserIcon" alt="" />
        <text-ellipsis class="ml-[8px] flex-1 text-[24px] text-desc">{{ teacher }}</text-ellipsis>
        <slot />
      </div>
    </div>
  </div>
</template>
