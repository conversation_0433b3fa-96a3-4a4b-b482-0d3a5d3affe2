<template>
  <div class="toast-fixed">
    <transition name="zoom">
      <div class="toast__body" v-if="isShow">{{ message }}aa</div>
    </transition>
  </div>
</template>
<script setup>
import { ref, onMounted } from 'vue';

const props = defineProps({
  isShow: Boolean,
  duration: Number,
  message: String,
});

let isShow = ref(props.isShow);

onMounted(() => {
  setTimeout(() => {
    isShow.value = !1;
  }, props.duration);
});
console.log('loading 创建了');
</script>

<style scoped>
.toast__body {
  color: #fff;
  font-size: 14px;
  max-width: 90%;
  padding: 13px 21px;
  border-radius: 2px;
  background-color: rgba(0, 0, 0, 0.6);
}

.toast-fixed {
  pointer-events: none;
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999 !important;
  background-color: transparent;
}

.zoom-leave-active,
.zoom-enter-active {
  transition: all 0.3s ease;
}

.zoom-enter,
.zoom-leave-to {
  transform: scale(1.2);
}

.zoom-enter-to,
.zoom-leave {
  transform: scale(1);
}
</style>
