<script setup>
import { computed } from 'vue';

const props = defineProps({
  type: {
    validator(value) {
      return ['success', 'danger', 'primary', 'default', 'tip'].includes(value);
    },
  },
  message: {
    type: String,
    required: true,
  },
});

const boxClass = computed(() => {
  return {
    'bg-bgGreen': props.type === 'success',
    'bg-bgOrange': props.type === 'danger',
    'bg-bgBlue': props.type === 'primary',
    'bg-bgGrey': props.type === 'default',
    'bg-black bg-opacity-50': props.type === 'tip',
  };
});

const textClass = computed(() => {
  return {
    'text-green': props.type === 'success',
    'text-deepOrange': props.type === 'danger',
    'text-blue': props.type === 'primary',
    'text-simple': props.type === 'default',
    'text-white': props.type === 'tip',
  };
});
</script>

<template>
  <div class="tag-style" :class="boxClass">
    <span class="tag-text" :class="textClass">{{ props.message }}</span>
  </div>
</template>

<style scoped lang="scss">
.tag-style {
  @apply px-2 leading-4 rounded-[8px];
}

.tag-text {
  @apply text-[22px];
}
</style>
