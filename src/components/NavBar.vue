<script setup>
import BackIcon from '@/assets/back.png';
import { appBack } from '@/utils';
import { useRouter, useRoute } from 'vue-router';

const router = useRouter();
const route = useRoute();

const props = defineProps({
  title: {
    type: String,
    default: '',
  },
  transparent: {
    type: Boolean,
    default: false,
  },
});
</script>

<template>
  <div class="grid h-[80px] bg-white items-center" style="grid-template-columns: 90px 1fr 90px" :class="{ 'bg-transparent': transparent }">
    <!-- 左侧返回按钮 -->
    <div class="pl-[32px]">
      <img :src="BackIcon" alt="" class="w-[48px] h-[48px]" @click="appBack(router, route)" />
    </div>

    <!-- 中间标题 -->
    <div class="min-w-0">
      <div class="text-[32px] font-medium text-[#1D2129]/90 leading-[48px] text-center truncate">{{ title }}</div>
    </div>

    <!-- 右侧插槽 -->
    <div class="pr-[32px] flex justify-end">
      <slot name="right"></slot>
    </div>
  </div>
</template>
