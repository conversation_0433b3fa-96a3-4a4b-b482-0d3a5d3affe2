<template>
  <div class="tab-bar">
    <van-tabbar route>
      <van-tabbar-item replace to="/">
        <span>主页</span>
        <template #icon="props">
          <img :src="props.active ? mainIcons.active : mainIcons.inactive" />
        </template>
      </van-tabbar-item>
      <van-tabbar-item replace to="/course">
        <span>课表</span>
        <template #icon="props">
          <img :src="props.active ? courseIcons.active : courseIcons.inactive" />
        </template>
      </van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { useBaseStore } from '@/store';
import courseIconActive from '@/assets/base/course_active.png';
import courseIcon from '@/assets/base/course.png';
import mainIconActive from '@/assets/base/home_active.png';
import mainIcon from '@/assets/base/home.png';
const router = useRouter();
const baseStore = useBaseStore();

const active = ref(baseStore.currentTabBarIndex);
const mainIcons = {
  active: mainIconActive,
  inactive: mainIcon,
};

const courseIcons = {
  active: courseIconActive,
  inactive: courseIcon,
};

const onChange = (index) => {
  baseStore.currentTabBarIndex = index;
  if (index === 0) {
    router.push('/');
  } else if (index === 1) {
    router.push('/course');
  }
};
</script>

<style lang="scss" scoped></style>
