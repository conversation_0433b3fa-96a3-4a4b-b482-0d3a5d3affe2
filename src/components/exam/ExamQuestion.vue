<script setup>
import { ref, defineProps, computed, defineEmits } from 'vue';
import AnalyzeBg from '@/assets/exam/analyze-bg.png';
import { QuestionModeEnum } from './constant';
import QuestionTypeTag from './QuestionTypeTag.vue';

const props = defineProps({
  question: {
    type: Object,
    required: true,
  },
  immediateSubmit: {
    // 是否立即提交，题库是立即提交，考试不是。
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['select']);

const optionStatusEnum = {
  SIMPLE: 'SIMPLE',
  SELECTED: 'SELECTED',
  WRONG: 'WRONG',
};

const optionStatus = ref(optionStatusEnum.SIMPLE);

// 选中的选项ID数组
const selectedOptions = ref([]);

// 处理选项列表，判断题自动生成对错选项
const questionOptions = computed(() => {
  if (props.question.type === 'T_OR_F') {
    return [
      {
        id: 'T',
        tag: 'T',
        optionText: '对',
      },
      {
        id: 'F',
        tag: 'F',
        optionText: '错',
      },
    ];
  }
  return props.question.questionOptions;
});

// 是否为多选题
const isMultiChoice = computed(() => {
  return props.question.type === 'MULTI';
});

// 选择选项
const selectOption = (option) => {
  if (isMultiChoice.value) {
    // 多选题处理
    const index = selectedOptions.value.indexOf(option.id);
    if (index > -1) {
      selectedOptions.value.splice(index, 1);
    } else {
      selectedOptions.value.push(option.id);
    }
    // 如果是立即提交，直接返回，不触发事件
    if (props.immediateSubmit) return;
  } else {
    // 单选题和判断题处理
    selectedOptions.value = [option.id];
  }

  emit('select', {
    answers: [option.id],
    questionId: props.question.id,
    type: props.question.type,
  });
};

// 判断选项是否被选中
const isOptionSelected = (optionId) => {
  return selectedOptions.value.includes(optionId);
};

// 多选题提交
const selectMultiOption = () => {
  if (isMultiChoice.value) {
    emit('select', {
      answers: [...selectedOptions.value],
      questionId: props.question.id,
      type: props.question.type,
    });
  }
};

// id字符串转tag字符串
function idsToTags(ids) {
  if (!ids) return '';
  return ids
    .split(',')
    .map((id) => questionOptions.value.find((opt) => opt.id === id)?.tag)
    .filter(Boolean)
    .join(',');
}

// 我的答案数组
const myAnswerArr = computed(() => (props.question.myAnswer ? props.question.myAnswer.split(',') : []));
// 正确答案数组
const rightAnswerArr = computed(() => (props.question.answer ? props.question.answer.split(',') : []));

// 我的答案标签及颜色
const myAnswerTagColors = computed(() => {
  return myAnswerArr.value
    .map((id) => {
      const tag = questionOptions.value.find((opt) => opt.id === id)?.tag;
      if (!tag) return null;
      // 正确答案优先
      if (rightAnswerArr.value.includes(id)) {
        return { tag, color: '#2C67EC' };
      } else {
        return { tag, color: '#FF3737' };
      }
    })
    .filter(Boolean);
});

// 我的答案标签
const myAnswerTags = computed(() => idsToTags(props.question.myAnswer));
// 正确答案标签
const rightAnswerTags = computed(() => idsToTags(props.question.answer));
</script>

<template>
  <div class="p-[32px] w-full bg-white overflow-y-auto">
    <!-- 题型 -->
    <div class="flex items-center justify-between">
      <QuestionTypeTag :type="question.type" />
      <slot name="right"></slot>
    </div>

    <!-- 标题 -->
    <p class="mt-[24px] text-[28px] font-medium leading-[48px] text-[#000000]/90">{{ question.stem }}</p>

    <!-- 选项 -->
    <div
      v-for="option in questionOptions"
      :key="option.id"
      class="mt-[24px] p-[16px] flex gap-[16px] min-h-[48px] rounded-[12px] text-[28px] leading-[48px] text-[#000000]/90 cursor-pointer"
      :class="{
        'bg-[#FFF6F6] text-[#FF3737]':
          question.mode === QuestionModeEnum.READ &&
          !question.isRight &&
          !question.answer.includes(option.id) &&
          question.myAnswer.includes(option.id),
        'bg-[#EEF3FF] text-[#2C67EC]':
          isOptionSelected(option.id) || (question.mode === QuestionModeEnum.READ && question.answer.includes(option.id)),
      }"
      @click="question.mode !== QuestionModeEnum.READ && selectOption(option)"
    >
      <span class="font-medium">{{ option.tag }}</span>
      <div class="h-[40px] w-[2px] bg-[#E5E5E5]"></div>
      <span>{{ option.optionText }}</span>
    </div>

    <!-- 多选题提交按钮 -->
    <button
      v-if="isMultiChoice && immediateSubmit && question.mode !== QuestionModeEnum.READ"
      class="mt-[48px] w-full py-[26px] bg-[#2C67EC] text-white text-[32px] font-medium leading-[44px] rounded-[16px]"
      @click="selectMultiOption"
    >
      确定
    </button>

    <!-- 解析 -->
    <div
      v-if="question.mode === QuestionModeEnum.READ"
      class="mt-[24px] p-[16px] rounded-[16px]"
      :style="{ background: `url(${AnalyzeBg}) no-repeat center top / 100% auto, linear-gradient(to bottom, #E6EEFF, #F3F8FE)` }"
    >
      <div class="flex h-[48px] items-center">
        <div class="mx-[16px] w-[8px] h-[28px] rounded-full bg-[#2C67EC]"></div>
        <span class="text-[28px] font-medium leading-[48px]">本题解析</span>
      </div>

      <div class="mt-[16px] p-[16px] text-[28px] leading-[48px] text-[#000000]/90 bg-white rounded-[12px]">
        <span>{{ question.analysis }}</span>
        <div class="mt-[24px] p-[24px] flex gap-[24px] bg-[#EEF1F6] rounded-[16px]">
          <div class="flex-1 flex gap-[16px] items-center">
            <div class="w-[12px] h-[12px] rounded-full" :class="[question.isRight ? 'bg-[#2C67EC]' : 'bg-[#FF3737]']"></div>
            <span>我的答案:</span>
            <span>
              <template v-for="(item, idx) in myAnswerTagColors" :key="item.tag">
                <span :style="{ color: item.color, 'font-weight': 'bold' }">{{ item.tag }}</span
                ><span v-if="idx < myAnswerTagColors.length - 1">,</span>
              </template>
            </span>
          </div>
          <div class="flex-1 flex gap-[16px] items-center">
            <div class="w-[12px] h-[12px] rounded-full bg-[#2C67EC]"></div>
            <span>正确答案:</span>
            <span class="font-medium text-[#2C67EC]">{{ rightAnswerTags }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
