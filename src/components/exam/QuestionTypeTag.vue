<script setup>
const props = defineProps({
  type: {
    type: String,
    required: true,
  },
});

// 获取题型显示文本
const getQuestionTypeText = (type) => {
  const typeMap = {
    SINGLE: '单选题',
    MULTI: '多选题',
    T_OR_F: '判断题',
  };
  return typeMap[type] || '未知题型';
};
</script>

<template>
  <div class="px-[12px] py-[4px] rounded-[8px] bg-[#DFE9FF] text-[24px] text-[#2C67EC] leading-[36px]">
    {{ getQuestionTypeText(type) }}
  </div>
</template>
