<script setup>
import { ref, computed } from 'vue';

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  activeQuestion: {
    type: String,
    default: '',
  },
  cardList: {
    type: Object,
    default: () => null,
  },
});

const emit = defineEmits(['update:modelValue', 'select']);

const showBottom = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});
</script>

<template>
  <van-popup v-model:show="showBottom" round position="bottom" :style="{ height: '60%' }" class="flex flex-col">
    <div class="h-[80px] leading-[80px] text-center text-[28px] font-medium text-[#222222]">答题卡</div>
    <div class="flex-1 overflow-y-auto px-[24px]">
      <div class="grid grid-cols-5 gap-y-[24px] py-[24px]">
        <div v-for="(key, num) in Object.keys(cardList || {})" :key="key" @click="emit('select', key)" class="flex items-center justify-center">
          <div
            class="flex items-center justify-center w-[72px] h-[72px] rounded-[12px] text-[28px] leading-[44px] text-[#222222]/90"
            :class="{
              'border border-[#2C67EC]': key === activeQuestion && (!cardList[key] || cardList[key].isRight),
              'border border-[#FF3737]': key === activeQuestion && cardList[key] && !cardList[key].isRight,
              'text-[#2C67EC] bg-[#EEF3FF]': cardList[key]?.isRight,
              'text-[#FF3737] bg-[#FFF6F6]': cardList[key] && !cardList[key].isRight,
            }"
          >
            {{ num + 1 }}
          </div>
        </div>
      </div>
    </div>
  </van-popup>
</template>
