<script setup>
import Tag from './Tag.vue';

const props = defineProps({
  title: {
    type: String,
    default: '课程列表',
  },
  rightText: {
    type: String,
  },
  list: {
    type: Array,
    required: true,
  },
});
const emit = defineEmits(['cardClick']);

function cardClick(id) {
  emit('cardClick', id);
}
</script>

<template>
  <div class="bg-white w-full p-[32px] mt-[24px]">
    <!--  标题  -->
    <div class="flex items-center mb-[24px]">
      <div class="flex items-center">
        <img class="h-[30px] mr-[17px]" src="@/assets/comps/course-panel/title-icon.png" alt="" />
        <div class="w-[150px] text-[32px] font-font font-semibold text-titleColor">
          {{ props.title }}
        </div>
      </div>
      <div v-if="props.rightText">class="desc-text">{{ props.rightText }}</div>
    </div>
    <!--  列表  -->
    <template v-for="item in props.list" :key="item.id">
      <div class="relative flex mb-[32px]" @click="cardClick(item.id)">
        <div class="relative w-[240px] h-[176px] mr-[32px] overflow-hidden bg-amber-100">
          <img class="w-full h-full rounded-[10px]" :src="item.thumb" alt="" />
          <tag v-if="item.isParticipate" class="absolute bottom-0 right-0" type="tip" message="我的课程"></tag>
        </div>
        <div class="flex-1 flex flex-col">
          <div class="h-[95px] text-[30px] font-font font-normal text-titleColor my-text-two-lines-overflow">
            {{ item.title }}
          </div>
          <div class="flex items-center mb-[12px] w-[300px]">
            <img src="@/assets/comps/course-panel/category.png" class="w-[28px] h-[28px] mr-[16px]" alt="" />
            <span class="text-[26px] font-font font-normal text-descColor my-text-one-line-overflow">{{ item.categoryVo.name || '未分类' }}</span>
          </div>
          <div class="flex items-center w-[250px]">
            <img src="@/assets/comps/course-panel/user.png" class="w-[28px] h-[28px] mr-[16px]" alt="" />
            <span class="text-[26px] font-font font-normal text-descColor my-text-one-line-overflow">讲师 {{ item.teacher }}</span>
          </div>
        </div>
        <div class="absolute right-0 bottom-0 text-[26px] font-font font-normal text-descColor">
          {{ item.userCount }}
          人参加
        </div>
      </div>
    </template>
  </div>
</template>
