<script setup>
import { ref, onMounted, nextTick, watch } from 'vue';

const props = defineProps({
  showBorder: {
    type: Boolean,
    default: false,
  },
  modelValue: {
    type: String,
    default: 'all',
  },
  tabs: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(['update:modelValue', 'change']);

const indicatorStyle = ref({
  left: '0px',
  width: '0px',
});

const updateIndicator = async (value) => {
  emit('update:modelValue', value);
  const currentTab = props.tabs.find((tab) => tab.value === value);
  if (!currentTab) return;
  emit('change', currentTab);

  await nextTick();
  checkOffset(value);
};

function checkOffset(value) {
  const activeTab = document.querySelector(`[data-tab="${value}"]`);
  if (activeTab) {
    const { offsetLeft, offsetWidth } = activeTab;
    indicatorStyle.value = {
      left: `${offsetLeft + (offsetWidth - 24) / 2}px`,
      width: '24px',
    };
  }
}

onMounted(() => {
  checkOffset(props.modelValue);
});

watch([() => props.tabs, () => props.modelValue], () => {
  updateIndicator(props.modelValue);
});
</script>

<template>
  <div
    class="relative flex overflow-x-auto scrollbar-none whitespace-nowrap px-[32px] text-[32px]"
    :class="{ 'border-b border-[rgba(0, 0, 0, 0.05)] border-solid': showBorder }"
  >
    <div
      v-for="tab in tabs"
      :key="tab.value"
      class="p-[16px] shrink-0"
      :class="modelValue === tab.value ? 'font-medium text-primary' : 'font-normal text-desc'"
      :data-tab="tab.value"
      @click="emit('update:modelValue', tab.value)"
    >
      {{ tab.label }}
    </div>
    <div class="absolute bottom-0 h-[8px] bg-primary rounded-sm transition-all duration-300" :style="indicatorStyle"></div>
  </div>
</template>

<style scoped>
.scrollbar-none {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.scrollbar-none::-webkit-scrollbar {
  display: none;
}
</style>
