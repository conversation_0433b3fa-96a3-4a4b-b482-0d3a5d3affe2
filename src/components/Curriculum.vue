<template>
  <div class="curr-warp">
    <div class="time">
      <span>{{ parseTime(courseData.startTime) }}</span>
      <span>开课</span>
    </div>
    <div class="line"></div>
    <div class="right">
      <div class="curr-info">
        <div class="title">{{ courseData.content }}</div>
        <div class="curr-msg">
          <div class="item">
            <img src="@/assets/base/person.png" alt="" />
            <span>讲师 {{ courseData.teachers }}</span>
          </div>
          <div class="item" v-if="!courseData.isTemporary">
            <img src="@/assets/base/time.png" alt="" />
            <span>{{ parseTimeToDate(courseData.startTime) }}- {{ parseTimeToDate(courseData.endTime) }}</span>
          </div>
          <div v-else>
            <img src="@/assets/base/time.png" alt="" />
            <span>{{ parseTimeTomin(courseData.startTime) }}</span>
          </div>
        </div>
      </div>
      <div class="go-look" v-if="courseState === '已开课'">
        <span @click="goLook(courseData.id, courseData.type, courseData.timeId)">前往观看</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import { parseTime, parseTimeToDate, getCourseState, parseTimeTomin } from '@/utils/index.js';

const props = defineProps({
  courseData: Object,
});

const courseState = getCourseState(props.courseData.startTime, props.courseData.endTime, props.courseData.type, props.courseData.status);

// console.log('课程状态', courseState)

onMounted(() => {});
const router = useRouter();
const goLook = (id, type, timeId) => {
  console.log('课程id是', id);
  router.push({
    path: '/detail',
    query: {
      courseId: id,
      type: type,
      timeId,
    },
  });
};
</script>

<style lang="scss" scoped>
.curr-warp {
  width: 99%;
  height: 200px;
  background-color: #fff;
  box-shadow: 0px 4px 16px 0px rgba(238, 238, 238, 1);
  display: flex;
  border-radius: 10px;
  .time {
    width: 125px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    span {
      text-align: center;
    }
    :nth-child(1) {
      font-size: 32px;
      font-family: DINAlternate-Bold, DINAlternate;
      font-weight: bold;
      color: #111b32;
    }
    :nth-child(2) {
      font-size: 24px;
      font-family:
        PingFangSC-Regular,
        PingFang SC;
      font-weight: 400;
      color: #444956;
    }
  }
  .line {
    // height: 100%;
    // width: 2px;
    // background-color: rgb(219, 219, 219);

    width: 6px;
    height: 200px;
    opacity: 0.5;
    background-color: #f3f4f8;
  }
  .right {
    flex: 1;
    display: flex;

    .curr-info {
      flex: 9;
      padding-left: 35px;
      .title {
        margin-top: 30px;
        font-size: 28px;
        font-family:
          PingFangSC-Semibold,
          PingFang SC;
        font-weight: 600;
        color: #23252a;
      }
      .curr-msg {
        margin-top: 28px;
        display: flex;
        flex-direction: column;

        span {
          font-size: 26px;
          font-family:
            PingFangSC-Regular,
            PingFang SC;
          font-weight: 400;
          color: #444956;
          // vertical-align: middle;
        }
        img {
          margin-right: 17px;
          width: 24px;
          vertical-align: middle;
        }
      }
    }
    .go-look {
      flex: 4;
      display: flex;
      align-items: center;
      span {
        font-size: 26px;
        color: #326fff;
        display: inline-block;
        width: 130px;
        height: 40px;
        border: 1px solid #326fff;
        line-height: 40px;
        text-align: center;
        border-radius: 20px;
      }
    }
  }
}
</style>
