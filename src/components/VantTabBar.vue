<script setup>
import { useRedDotStore } from '@/store';
import { onUnmounted } from 'vue';
import { onMounted } from 'vue';
import { ref } from 'vue';

const redDotStore = useRedDotStore();

const active = ref(0);

onMounted(() => {
  const activeItem = sessionStorage.getItem('__active__');
  if (activeItem) {
    active.value = Number(activeItem);
  }
});

onUnmounted(() => {
  sessionStorage.setItem('__active__', active.value);
});

const homeIcon = {
  active: require('@/assets/base/home_active.png'),
  inactive: require('@/assets/base/home.png'),
};
const courseIcon = {
  active: require('@/assets/base/course_active.png'),
  inactive: require('@/assets/base/course.png'),
};
const examIcon = {
  active: require('@/assets/base/exam_active.png'),
  inactive: require('@/assets/base/exam.png'),
};
const myBankIcon = {
  active: require('@/assets/base/my_bank_active.png'),
  inactive: require('@/assets/base/my_bank.png'),
};
const personIcon = {
  active: require('@/assets/base/person_active.png'),
  inactive: require('@/assets/base/person.png'),
};
</script>

<template>
  <van-tabbar v-model="active">
    <van-tabbar-item to="/">
      <span>课程中心</span>
      <template #icon="props">
        <img :src="props.active ? homeIcon.active : homeIcon.inactive" />
      </template>
    </van-tabbar-item>
    <van-tabbar-item to="/course-table">
      <van-badge :content="redDotStore.planNum" max="99" :show-zero="false" :offset="[0, -20]">
        <span>课表</span>
      </van-badge>
      <template #icon="props">
        <img :src="props.active ? courseIcon.active : courseIcon.inactive" />
      </template>
    </van-tabbar-item>
    <van-tabbar-item to="/my-exam">
      <van-badge :content="redDotStore.testNum" max="99" :show-zero="false" :offset="[0, -20]">
        <span>考试</span>
      </van-badge>
      <template #icon="props">
        <img :src="props.active ? examIcon.active : examIcon.inactive" />
      </template>
    </van-tabbar-item>
    <van-tabbar-item to="/my-bank">
      <span>题库</span>
      <template #icon="props">
        <img :src="props.active ? myBankIcon.active : myBankIcon.inactive" />
      </template>
    </van-tabbar-item>
    <van-tabbar-item to="/person-page">
      <span>个人中心</span>
      <template #icon="props">
        <img :src="props.active ? personIcon.active : personIcon.inactive" />
      </template>
    </van-tabbar-item>
  </van-tabbar>
</template>
