<template>
  <div class="itemWarp flex_mid" :class="{ active: bol }" @click="changePage">
    <div v-show="!bol">
      <slot name="img"></slot>
    </div>
    <div v-show="bol">
      <slot name="onImg"></slot>
    </div>
    <div v-text="txt" class="p"></div>
  </div>
</template>

<script>
import { mapState } from 'pinia';
import { useBaseStore } from '@/store/base.js';

export default {
  name: 'Item',
  data() {
    return {};
  },
  props: {
    txt: {
      type: String,
    },
    page: {
      type: String,
    },
    num: {
      type: Number,
    },
  },
  computed: {
    ...mapState(useBaseStore, {
      currentTabBarIndex: 'currentTabBarIndex',
    }),
    bol() {
      if (this.currentTabBarIndex == this.num) {
        return true;
      }
      return false;
    },
  },
  methods: {
    changePage() {
      console.log('点击了跳转:', this.num);
      //点击跳转对应的页面
      // this.$router.push("/" + this.page);
      if (this.num === 0) {
        this.$router.replace({
          path: '/',
        });
      } else if (this.num === 1) {
        this.$router.replace({
          path: '/course',
        });
      }
      this.$emit('change', this.num);
    },
  },
};
</script>

<style type="text/css">
.itemWarp {
  width: 33%;
  text-align: center;
  box-sizing: border-box;
  color: #999999;
  margin: 14px 0;
}
.itemWarp div img {
  width: 33px;
  height: 35px;
}
.itemWarp div.p {
  height: 93px;
  font-size: 23px;
  font-family: PingFang SC;
  font-weight: 400;
  /* color: #999999; */
  margin-top: 12px;
}
.active div {
  color: #2479ed;
}
</style>
]
