/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ['./src/**/*.{vue,js,ts,jsx,tsx}', './public/index.html'],
  theme: {
    extend: {
      colors: {
        primary: '#326FFF',
        black: '#23252A',
        desc: '#878B95',
        blue: '#2C67EC',
        white: '#FFFFFF',
        lightBlue: '#E8F4FF',
        borderGrey: '#E5E6EB',
        delete: '#F86022',
        page: '#F5F6F7',
        // delete
        grey: '#F5F6F7',
        titleColor: '#23252A',
        descColor: '#878B95',
        simple: '#444956',
        border: '#E4E9EF',
        green: '#05AE26',
        red: '#EC5959',
        orange: '#FF8035',
        deepOrange: '#FF461C',
        bgBlue: 'rgba(50,111,255,0.1)',
        bgGreen: 'rgba(0, 191, 61, 0.10)',
        bgGrey: 'rgba(71, 71, 71, 0.10)',
        bgOrange: 'rgba(255, 70, 28, 0.10)',
      },
      fontFamily: {
        base: ['PingFang SC', 'sans-serif'],
        // delete
        font: ['PingFang SC', 'sans-serif'],
        youShe: ['YouShe', 'sans-serif'],
      },
      fontSize: {
        sm: '12px',
        base: '14px',
        md: '16px',
        lg: '18px',
        xl: '20px',
      },
      borderRadius: {
        base: '8px',
        sm: '4px',
        md: '12px',
        lg: '16px',
        xl: '20px',
      },
    },
  },
  plugins: [],
};
