const { defineConfig } = require('@vue/cli-service');

const { VantResolver } = require('unplugin-vue-components/resolvers');
const ComponentsPlugin = require('unplugin-vue-components/webpack');
const VUE_APP_NODE = process.env.VUE_APP_NODE;
let publicPath = '/train/';
if (VUE_APP_NODE === 'dev-build') {
  console.log('原来是dev环境啊');
  publicPath = '/ddbes-train-mobile/';
}

module.exports = defineConfig({
  transpileDependencies: true,
  outputDir: 'train',
  publicPath: publicPath,
  configureWebpack: {
    plugins: [
      ComponentsPlugin({
        resolvers: [VantResolver()],
      }),
    ],
  },
});
